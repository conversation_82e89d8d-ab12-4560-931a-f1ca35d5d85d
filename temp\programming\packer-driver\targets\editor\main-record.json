{"modules": {"cce:/internal/x/cc": {"mTimestamp": 3711.9054, "chunkId": "93ba276ea7b26ffcdc433fab14afc1ed6f05647b", "imports": [{"value": "cce:/internal/x/cc-fu/base", "resolved": "__unresolved_0", "loc": {"start": {"line": 1, "column": 14}, "end": {"line": 1, "column": 42}}}, {"value": "cce:/internal/x/cc-fu/gfx-webgl", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 14}, "end": {"line": 2, "column": 47}}}, {"value": "cce:/internal/x/cc-fu/gfx-webgl2", "resolved": "__unresolved_2", "loc": {"start": {"line": 3, "column": 14}, "end": {"line": 3, "column": 48}}}, {"value": "cce:/internal/x/cc-fu/gfx-empty", "resolved": "__unresolved_3", "loc": {"start": {"line": 4, "column": 14}, "end": {"line": 4, "column": 47}}}, {"value": "cce:/internal/x/cc-fu/gfx-webgpu", "resolved": "__unresolved_4", "loc": {"start": {"line": 5, "column": 14}, "end": {"line": 5, "column": 48}}}, {"value": "cce:/internal/x/cc-fu/3d", "resolved": "__unresolved_5", "loc": {"start": {"line": 6, "column": 14}, "end": {"line": 6, "column": 40}}}, {"value": "cce:/internal/x/cc-fu/animation", "resolved": "__unresolved_6", "loc": {"start": {"line": 7, "column": 14}, "end": {"line": 7, "column": 47}}}, {"value": "cce:/internal/x/cc-fu/skeletal-animation", "resolved": "__unresolved_7", "loc": {"start": {"line": 8, "column": 14}, "end": {"line": 8, "column": 56}}}, {"value": "cce:/internal/x/cc-fu/2d", "resolved": "__unresolved_8", "loc": {"start": {"line": 9, "column": 14}, "end": {"line": 9, "column": 40}}}, {"value": "cce:/internal/x/cc-fu/sorting", "resolved": "__unresolved_9", "loc": {"start": {"line": 10, "column": 14}, "end": {"line": 10, "column": 45}}}, {"value": "cce:/internal/x/cc-fu/rich-text", "resolved": "__unresolved_10", "loc": {"start": {"line": 11, "column": 14}, "end": {"line": 11, "column": 47}}}, {"value": "cce:/internal/x/cc-fu/mask", "resolved": "__unresolved_11", "loc": {"start": {"line": 12, "column": 14}, "end": {"line": 12, "column": 42}}}, {"value": "cce:/internal/x/cc-fu/graphics", "resolved": "__unresolved_12", "loc": {"start": {"line": 13, "column": 14}, "end": {"line": 13, "column": 46}}}, {"value": "cce:/internal/x/cc-fu/ui-skew", "resolved": "__unresolved_13", "loc": {"start": {"line": 14, "column": 14}, "end": {"line": 14, "column": 45}}}, {"value": "cce:/internal/x/cc-fu/ui", "resolved": "__unresolved_14", "loc": {"start": {"line": 15, "column": 14}, "end": {"line": 15, "column": 40}}}, {"value": "cce:/internal/x/cc-fu/affine-transform", "resolved": "__unresolved_15", "loc": {"start": {"line": 16, "column": 14}, "end": {"line": 16, "column": 54}}}, {"value": "cce:/internal/x/cc-fu/particle", "resolved": "__unresolved_16", "loc": {"start": {"line": 17, "column": 14}, "end": {"line": 17, "column": 46}}}, {"value": "cce:/internal/x/cc-fu/particle-2d", "resolved": "__unresolved_17", "loc": {"start": {"line": 18, "column": 14}, "end": {"line": 18, "column": 49}}}, {"value": "cce:/internal/x/cc-fu/physics-framework", "resolved": "__unresolved_18", "loc": {"start": {"line": 19, "column": 14}, "end": {"line": 19, "column": 55}}}, {"value": "cce:/internal/x/cc-fu/physics-cannon", "resolved": "__unresolved_19", "loc": {"start": {"line": 20, "column": 14}, "end": {"line": 20, "column": 52}}}, {"value": "cce:/internal/x/cc-fu/physics-physx", "resolved": "__unresolved_20", "loc": {"start": {"line": 21, "column": 14}, "end": {"line": 21, "column": 51}}}, {"value": "cce:/internal/x/cc-fu/physics-ammo", "resolved": "__unresolved_21", "loc": {"start": {"line": 22, "column": 14}, "end": {"line": 22, "column": 50}}}, {"value": "cce:/internal/x/cc-fu/physics-builtin", "resolved": "__unresolved_22", "loc": {"start": {"line": 23, "column": 14}, "end": {"line": 23, "column": 53}}}, {"value": "cce:/internal/x/cc-fu/physics-2d-framework", "resolved": "__unresolved_23", "loc": {"start": {"line": 24, "column": 14}, "end": {"line": 24, "column": 58}}}, {"value": "cce:/internal/x/cc-fu/physics-2d-box2d-jsb", "resolved": "__unresolved_24", "loc": {"start": {"line": 25, "column": 14}, "end": {"line": 25, "column": 58}}}, {"value": "cce:/internal/x/cc-fu/physics-2d-box2d", "resolved": "__unresolved_25", "loc": {"start": {"line": 26, "column": 14}, "end": {"line": 26, "column": 54}}}, {"value": "cce:/internal/x/cc-fu/physics-2d-builtin", "resolved": "__unresolved_26", "loc": {"start": {"line": 27, "column": 14}, "end": {"line": 27, "column": 56}}}, {"value": "cce:/internal/x/cc-fu/physics-2d-box2d-wasm", "resolved": "__unresolved_27", "loc": {"start": {"line": 28, "column": 14}, "end": {"line": 28, "column": 59}}}, {"value": "cce:/internal/x/cc-fu/intersection-2d", "resolved": "__unresolved_28", "loc": {"start": {"line": 29, "column": 14}, "end": {"line": 29, "column": 53}}}, {"value": "cce:/internal/x/cc-fu/primitive", "resolved": "__unresolved_29", "loc": {"start": {"line": 30, "column": 14}, "end": {"line": 30, "column": 47}}}, {"value": "cce:/internal/x/cc-fu/profiler", "resolved": "__unresolved_30", "loc": {"start": {"line": 31, "column": 14}, "end": {"line": 31, "column": 46}}}, {"value": "cce:/internal/x/cc-fu/geometry-renderer", "resolved": "__unresolved_31", "loc": {"start": {"line": 32, "column": 14}, "end": {"line": 32, "column": 55}}}, {"value": "cce:/internal/x/cc-fu/audio", "resolved": "__unresolved_32", "loc": {"start": {"line": 33, "column": 14}, "end": {"line": 33, "column": 43}}}, {"value": "cce:/internal/x/cc-fu/video", "resolved": "__unresolved_33", "loc": {"start": {"line": 34, "column": 14}, "end": {"line": 34, "column": 43}}}, {"value": "cce:/internal/x/cc-fu/xr", "resolved": "__unresolved_34", "loc": {"start": {"line": 35, "column": 14}, "end": {"line": 35, "column": 40}}}, {"value": "cce:/internal/x/cc-fu/light-probe", "resolved": "__unresolved_35", "loc": {"start": {"line": 36, "column": 14}, "end": {"line": 36, "column": 49}}}, {"value": "cce:/internal/x/cc-fu/terrain", "resolved": "__unresolved_36", "loc": {"start": {"line": 37, "column": 14}, "end": {"line": 37, "column": 45}}}, {"value": "cce:/internal/x/cc-fu/webview", "resolved": "__unresolved_37", "loc": {"start": {"line": 38, "column": 14}, "end": {"line": 38, "column": 45}}}, {"value": "cce:/internal/x/cc-fu/tween", "resolved": "__unresolved_38", "loc": {"start": {"line": 39, "column": 14}, "end": {"line": 39, "column": 43}}}, {"value": "cce:/internal/x/cc-fu/tiled-map", "resolved": "__unresolved_39", "loc": {"start": {"line": 40, "column": 14}, "end": {"line": 40, "column": 47}}}, {"value": "cce:/internal/x/cc-fu/vendor-google", "resolved": "__unresolved_40", "loc": {"start": {"line": 41, "column": 14}, "end": {"line": 41, "column": 51}}}, {"value": "cce:/internal/x/cc-fu/spine", "resolved": "__unresolved_41", "loc": {"start": {"line": 42, "column": 14}, "end": {"line": 42, "column": 43}}}, {"value": "cce:/internal/x/cc-fu/dragon-bones", "resolved": "__unresolved_42", "loc": {"start": {"line": 43, "column": 14}, "end": {"line": 43, "column": 50}}}, {"value": "cce:/internal/x/cc-fu/custom-pipeline", "resolved": "__unresolved_43", "loc": {"start": {"line": 44, "column": 14}, "end": {"line": 44, "column": 53}}}, {"value": "cce:/internal/x/cc-fu/custom-pipeline-post-process", "resolved": "__unresolved_44", "loc": {"start": {"line": 45, "column": 14}, "end": {"line": 45, "column": 66}}}, {"value": "cce:/internal/x/cc-fu/legacy-pipeline", "resolved": "__unresolved_45", "loc": {"start": {"line": 46, "column": 14}, "end": {"line": 46, "column": 53}}}], "type": "esm", "resolutions": [{"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/base"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/gfx-webgl"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/gfx-webgl2"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/gfx-empty"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/gfx-webgpu"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/3d"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/animation"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/skeletal-animation"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/2d"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/sorting"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/rich-text"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/mask"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/graphics"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/ui-skew"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/ui"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/affine-transform"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/particle"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/particle-2d"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/physics-framework"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/physics-cannon"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/physics-physx"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/physics-ammo"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/physics-builtin"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/physics-2d-framework"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/physics-2d-box2d-jsb"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/physics-2d-box2d"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/physics-2d-builtin"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/physics-2d-box2d-wasm"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/intersection-2d"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/primitive"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/profiler"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/geometry-renderer"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/audio"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/video"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/xr"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/light-probe"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/terrain"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/webview"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/tween"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/tiled-map"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/vendor-google"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/spine"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/dragon-bones"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/custom-pipeline"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/custom-pipeline-post-process"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cce:/internal/x/cc-fu/legacy-pipeline"}, "messages": []}]}, "cce:/internal/x/prerequisite-imports": {"mTimestamp": 14130367.8327, "chunkId": "6d8fd2b0177941b032ddc0733af48a561fb60657", "imports": [{"value": "file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts", "resolved": "__unresolved_0", "loc": {"start": {"line": 5, "column": 35}, "end": {"line": 5, "column": 174}}}, {"value": "file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts", "resolved": "__unresolved_1", "loc": {"start": {"line": 5, "column": 190}, "end": {"line": 5, "column": 334}}}, {"value": "file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts", "resolved": "__unresolved_2", "loc": {"start": {"line": 5, "column": 350}, "end": {"line": 5, "column": 498}}}, {"value": "file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts", "resolved": "__unresolved_3", "loc": {"start": {"line": 5, "column": 514}, "end": {"line": 5, "column": 659}}}, {"value": "file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts", "resolved": "__unresolved_4", "loc": {"start": {"line": 5, "column": 675}, "end": {"line": 5, "column": 814}}}, {"value": "file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts", "resolved": "__unresolved_5", "loc": {"start": {"line": 5, "column": 830}, "end": {"line": 5, "column": 962}}}, {"value": "file:///E:/M2Game/Client/assets/gizmos/EmitterGizmo.ts", "resolved": "__unresolved_6", "loc": {"start": {"line": 5, "column": 978}, "end": {"line": 5, "column": 1034}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/AutoGen/Luban/schema.ts", "resolved": "__unresolved_7", "loc": {"start": {"line": 5, "column": 1050}, "end": {"line": 5, "column": 1115}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/AutoGen/PB/cs_proto.ts", "resolved": "__unresolved_8", "loc": {"start": {"line": 5, "column": 1131}, "end": {"line": 5, "column": 1195}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/Game/Anim.ts", "resolved": "__unresolved_9", "loc": {"start": {"line": 5, "column": 1211}, "end": {"line": 5, "column": 1265}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/Game/Background.ts", "resolved": "__unresolved_10", "loc": {"start": {"line": 5, "column": 1281}, "end": {"line": 5, "column": 1341}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/Game/Enemy.ts", "resolved": "__unresolved_11", "loc": {"start": {"line": 5, "column": 1357}, "end": {"line": 5, "column": 1412}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/Game/EnemyBullet.ts", "resolved": "__unresolved_12", "loc": {"start": {"line": 5, "column": 1428}, "end": {"line": 5, "column": 1489}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/Game/GameOver.ts", "resolved": "__unresolved_13", "loc": {"start": {"line": 5, "column": 1505}, "end": {"line": 5, "column": 1563}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/Game/Global.ts", "resolved": "__unresolved_14", "loc": {"start": {"line": 5, "column": 1579}, "end": {"line": 5, "column": 1635}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/Game/Goods.ts", "resolved": "__unresolved_15", "loc": {"start": {"line": 5, "column": 1651}, "end": {"line": 5, "column": 1706}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/Game/MainGame.ts", "resolved": "__unresolved_16", "loc": {"start": {"line": 5, "column": 1722}, "end": {"line": 5, "column": 1780}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/Game/Menu.ts", "resolved": "__unresolved_17", "loc": {"start": {"line": 5, "column": 1796}, "end": {"line": 5, "column": 1850}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/Game/PersistNode.ts", "resolved": "__unresolved_18", "loc": {"start": {"line": 5, "column": 1866}, "end": {"line": 5, "column": 1927}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/Game/Player.ts", "resolved": "__unresolved_19", "loc": {"start": {"line": 5, "column": 1943}, "end": {"line": 5, "column": 1999}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/Game/PlayerBullet.ts", "resolved": "__unresolved_20", "loc": {"start": {"line": 5, "column": 2015}, "end": {"line": 5, "column": 2077}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/Game/factroy/AnimFactory.ts", "resolved": "__unresolved_21", "loc": {"start": {"line": 5, "column": 2093}, "end": {"line": 5, "column": 2162}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/Game/factroy/EnemyBulletFactory.ts", "resolved": "__unresolved_22", "loc": {"start": {"line": 5, "column": 2178}, "end": {"line": 5, "column": 2254}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/Game/factroy/EnemyFactory.ts", "resolved": "__unresolved_23", "loc": {"start": {"line": 5, "column": 2270}, "end": {"line": 5, "column": 2340}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/Game/factroy/GameFactory.ts", "resolved": "__unresolved_24", "loc": {"start": {"line": 5, "column": 2356}, "end": {"line": 5, "column": 2425}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/Game/factroy/GoodsFactory.ts", "resolved": "__unresolved_25", "loc": {"start": {"line": 5, "column": 2441}, "end": {"line": 5, "column": 2511}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/Game/factroy/PlayerBulletFactory.ts", "resolved": "__unresolved_26", "loc": {"start": {"line": 5, "column": 2527}, "end": {"line": 5, "column": 2604}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/Game/world/Bootstrap.ts", "resolved": "__unresolved_27", "loc": {"start": {"line": 5, "column": 2620}, "end": {"line": 5, "column": 2685}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/Game/world/WorldInitializeData.ts", "resolved": "__unresolved_28", "loc": {"start": {"line": 5, "column": 2701}, "end": {"line": 5, "column": 2776}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/Game/world/base/System.ts", "resolved": "__unresolved_29", "loc": {"start": {"line": 5, "column": 2792}, "end": {"line": 5, "column": 2859}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/Game/world/base/SystemContainer.ts", "resolved": "__unresolved_30", "loc": {"start": {"line": 5, "column": 2875}, "end": {"line": 5, "column": 2951}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/Game/world/base/TypeID.ts", "resolved": "__unresolved_31", "loc": {"start": {"line": 5, "column": 2967}, "end": {"line": 5, "column": 3034}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/Game/world/base/World.ts", "resolved": "__unresolved_32", "loc": {"start": {"line": 5, "column": 3050}, "end": {"line": 5, "column": 3116}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/Game/world/bullet/Bullet.ts", "resolved": "__unresolved_33", "loc": {"start": {"line": 5, "column": 3132}, "end": {"line": 5, "column": 3201}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/Game/world/bullet/BulletSystem.ts", "resolved": "__unresolved_34", "loc": {"start": {"line": 5, "column": 3217}, "end": {"line": 5, "column": 3292}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/Game/world/bullet/Emitter.ts", "resolved": "__unresolved_35", "loc": {"start": {"line": 5, "column": 3308}, "end": {"line": 5, "column": 3378}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/Game/world/index.ts", "resolved": "__unresolved_36", "loc": {"start": {"line": 5, "column": 3394}, "end": {"line": 5, "column": 3455}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/Game/world/level/LevelSystem.ts", "resolved": "__unresolved_37", "loc": {"start": {"line": 5, "column": 3471}, "end": {"line": 5, "column": 3544}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/Game/world/player/PlayerSystem.ts", "resolved": "__unresolved_38", "loc": {"start": {"line": 5, "column": 3560}, "end": {"line": 5, "column": 3635}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/GameInstance.ts", "resolved": "__unresolved_39", "loc": {"start": {"line": 5, "column": 3651}, "end": {"line": 5, "column": 3708}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/IMgr.ts", "resolved": "__unresolved_40", "loc": {"start": {"line": 5, "column": 3724}, "end": {"line": 5, "column": 3773}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/Luban/LubanMgr.ts", "resolved": "__unresolved_41", "loc": {"start": {"line": 5, "column": 3789}, "end": {"line": 5, "column": 3848}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/MainUI.ts", "resolved": "__unresolved_42", "loc": {"start": {"line": 5, "column": 3864}, "end": {"line": 5, "column": 3915}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/Network/NetMgr.ts", "resolved": "__unresolved_43", "loc": {"start": {"line": 5, "column": 3931}, "end": {"line": 5, "column": 3990}}}, {"value": "file:///E:/M2Game/Client/assets/scripts/ResUpdate/ResUpdate.ts", "resolved": "__unresolved_44", "loc": {"start": {"line": 5, "column": 4006}, "end": {"line": 5, "column": 4070}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/gizmos/EmitterGizmo.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/AutoGen/Luban/schema.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/AutoGen/PB/cs_proto.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/Anim.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/Background.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/Enemy.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/EnemyBullet.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/GameOver.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/Global.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/Goods.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/MainGame.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/Menu.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/PersistNode.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/Player.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/PlayerBullet.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/factroy/AnimFactory.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/factroy/EnemyBulletFactory.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/factroy/EnemyFactory.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/factroy/GameFactory.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/factroy/GoodsFactory.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/factroy/PlayerBulletFactory.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/world/Bootstrap.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/world/WorldInitializeData.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/world/base/System.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/world/base/SystemContainer.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/world/base/TypeID.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/world/base/World.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/world/bullet/Bullet.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/world/bullet/BulletSystem.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/world/bullet/Emitter.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/world/index.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/world/level/LevelSystem.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/world/player/PlayerSystem.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/GameInstance.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/IMgr.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Luban/LubanMgr.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/MainUI.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Network/NetMgr.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/ResUpdate/ResUpdate.ts"}, "messages": []}]}, "file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts": {"mTimestamp": {"mtime": 1752314493464.55, "uuid": "11f3130e-c08c-47bb-a209-71d114594e6d"}, "chunkId": "d208b01558c13077e4a9c9f1302dfbf2b2122e40", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 28, "column": 7}, "end": {"line": 28, "column": 11}}}, {"value": "cc/env", "loc": {"start": {"line": 30, "column": 23}, "end": {"line": 30, "column": 31}}}, {"value": "./builtin-pipeline-settings", "resolved": "__unresolved_1", "loc": {"start": {"line": 34, "column": 7}, "end": {"line": 34, "column": 36}}}, {"value": "./builtin-pipeline-pass", "resolved": "__unresolved_2", "loc": {"start": {"line": 38, "column": 7}, "end": {"line": 38, "column": 32}}}, {"value": "./builtin-pipeline", "resolved": "__unresolved_3", "loc": {"start": {"line": 45, "column": 7}, "end": {"line": 45, "column": 27}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cc/env"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts"}, "messages": []}]}, "file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts": {"mTimestamp": {"mtime": 1752314493468.5576, "uuid": "6f94083c-fc92-438b-a15b-a20ec61666c7"}, "chunkId": "b37de22ab9dff6e9f979ab9e517f60b5b4ae4ba5", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 30, "column": 7}, "end": {"line": 30, "column": 11}}}, {"value": "./builtin-pipeline-settings", "resolved": "__unresolved_1", "loc": {"start": {"line": 32, "column": 40}, "end": {"line": 32, "column": 69}}}, {"value": "cc/env", "loc": {"start": {"line": 34, "column": 23}, "end": {"line": 34, "column": 31}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cc/env"}, "messages": []}]}, "file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts": {"mTimestamp": {"mtime": 1752314493469.5603, "uuid": "de1c2107-70c8-4021-8459-6399f24d01c6"}, "chunkId": "36278e5c964e5e2f737bca1654894a5a7b2a7063", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 28, "column": 7}, "end": {"line": 28, "column": 11}}}, {"value": "cc/env", "loc": {"start": {"line": 30, "column": 23}, "end": {"line": 30, "column": 31}}}, {"value": "./builtin-pipeline-types", "resolved": "__unresolved_1", "loc": {"start": {"line": 34, "column": 7}, "end": {"line": 34, "column": 33}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cc/env"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts"}, "messages": []}]}, "file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts": {"mTimestamp": {"mtime": 1752314493469.5603, "uuid": "cbf30902-517f-40dc-af90-a550bac27cf1"}, "chunkId": "383c24386be9d9de15fc0c17a8951753b54d596a", "imports": [{"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 31, "column": 41}, "end": {"line": 31, "column": 45}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts": {"mTimestamp": {"mtime": 1752314493470.56, "uuid": "ff9b0199-ce04-4cfe-86cc-6c719f08d6e4"}, "chunkId": "9846cefb9cb6e16313f2e5e80bbb689314385757", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 29, "column": 7}, "end": {"line": 29, "column": 11}}}, {"value": "cc/env", "loc": {"start": {"line": 31, "column": 30}, "end": {"line": 31, "column": 38}}}, {"value": "./builtin-pipeline-types", "resolved": "__unresolved_1", "loc": {"start": {"line": 36, "column": 7}, "end": {"line": 36, "column": 33}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cc/env"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts"}, "messages": []}]}, "file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts": {"mTimestamp": {"mtime": 1752314493593.2278, "uuid": "b2bd1fa7-8d7c-49c5-a158-df29a6d3a594"}, "chunkId": "49c387c7d23ec5c771c1bd713cdd20f77551061d", "imports": [{"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 220}, "end": {"line": 1, "column": 224}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/AutoGen/Luban/schema.ts": {"mTimestamp": {"mtime": 1753501233443.8508, "uuid": "da084ae1-6b5b-4d7a-94a6-887fdbb7e052"}, "chunkId": "1afdb977ae03ab577533081b369d65c5f421dc5a", "imports": [{"value": "cc"}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/AutoGen/PB/cs_proto.ts": {"mTimestamp": {"mtime": 1753501233450.601, "uuid": "81097702-1c01-407d-aeb2-686f8e932e49"}, "chunkId": "dfc204b977d4344f0a5bc22fc1377aa6da31bb5d", "imports": [{"value": "cc"}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/Anim.ts": {"mTimestamp": {"mtime": 1753501233569.9827, "uuid": "5348a46b-267c-4023-8b84-3382eb48f3c8"}, "chunkId": "0a34faf9cb5d6c878a0460b1d91d2043833d1540", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 50}, "end": {"line": 1, "column": 54}}}, {"value": "./PersistNode", "resolved": "__unresolved_1", "loc": {"start": {"line": 3, "column": 28}, "end": {"line": 3, "column": 43}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/PersistNode.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/Background.ts": {"mTimestamp": {"mtime": 1753501233570.987, "uuid": "03439b13-5f90-4827-b94b-4a91188a5588"}, "chunkId": "f2351c12fef31ec43cb4aa7781de26c8a25c10f9", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 50}, "end": {"line": 1, "column": 54}}}, {"value": "./Global", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 23}, "end": {"line": 2, "column": 33}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/Global.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/Enemy.ts": {"mTimestamp": {"mtime": 1753501233572.197, "uuid": "c5c20e85-6542-4c38-8f04-f74386d5ec9f"}, "chunkId": "fc2fbfb1f30b2c72dbfc2a98e4a25bc281106361", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 182}, "end": {"line": 1, "column": 186}}}, {"value": "./Global", "resolved": "__unresolved_1", "loc": {"start": {"line": 4, "column": 23}, "end": {"line": 4, "column": 33}}}, {"value": "./PersistNode", "resolved": "__unresolved_2", "loc": {"start": {"line": 5, "column": 28}, "end": {"line": 5, "column": 43}}}, {"value": "./Player", "resolved": "__unresolved_3", "loc": {"start": {"line": 6, "column": 23}, "end": {"line": 6, "column": 33}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/Global.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/PersistNode.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/Player.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/EnemyBullet.ts": {"mTimestamp": {"mtime": 1753501233573.5295, "uuid": "c8821111-2211-493e-ba13-2fe76ae3fa1c"}, "chunkId": "04e35005b949273cf9021ee06cd2d1683a661e7c", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 164}, "end": {"line": 1, "column": 168}}}, {"value": "./Global", "resolved": "__unresolved_1", "loc": {"start": {"line": 3, "column": 23}, "end": {"line": 3, "column": 33}}}, {"value": "./PersistNode", "resolved": "__unresolved_2", "loc": {"start": {"line": 4, "column": 28}, "end": {"line": 4, "column": 43}}}, {"value": "./Player", "resolved": "__unresolved_3", "loc": {"start": {"line": 5, "column": 23}, "end": {"line": 5, "column": 33}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/Global.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/PersistNode.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/Player.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/GameOver.ts": {"mTimestamp": {"mtime": 1753501233574.144, "uuid": "382d6722-7526-4b22-8289-be056b91e1aa"}, "chunkId": "83e5ddb6ce44a461006c21ee690b042176de43bc", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 67}, "end": {"line": 1, "column": 71}}}, {"value": "./Global", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 23}, "end": {"line": 2, "column": 33}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/Global.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/Global.ts": {"mTimestamp": {"mtime": 1753501233575.0334, "uuid": "ffc9acad-fc44-4a0b-8e0d-6670b6c4e175"}, "chunkId": "a4972beeaf0fcdc8cd851bda6f7366e427f80e52", "imports": [{"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 50}, "end": {"line": 1, "column": 54}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/Goods.ts": {"mTimestamp": {"mtime": 1753501233576.038, "uuid": "279df947-049f-4830-9cf0-65d34ee16a7e"}, "chunkId": "dd956057b78450450753a6fd7a42c04d4d2cf3fa", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 136}, "end": {"line": 1, "column": 140}}}, {"value": "./Global", "resolved": "__unresolved_1", "loc": {"start": {"line": 3, "column": 23}, "end": {"line": 3, "column": 33}}}, {"value": "./PersistNode", "resolved": "__unresolved_2", "loc": {"start": {"line": 4, "column": 28}, "end": {"line": 4, "column": 43}}}, {"value": "./Player", "resolved": "__unresolved_3", "loc": {"start": {"line": 5, "column": 23}, "end": {"line": 5, "column": 33}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/Global.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/PersistNode.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/Player.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/MainGame.ts": {"mTimestamp": {"mtime": 1753501233576.8745, "uuid": "226be14a-b41d-43c8-86b2-bee80a67eb84"}, "chunkId": "60294c05f2abdf37c082915c18db52bac54df813", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 56}, "end": {"line": 1, "column": 60}}}, {"value": "./Global", "resolved": "__unresolved_1", "loc": {"start": {"line": 3, "column": 23}, "end": {"line": 3, "column": 33}}}, {"value": "./PersistNode", "resolved": "__unresolved_2", "loc": {"start": {"line": 4, "column": 28}, "end": {"line": 4, "column": 43}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/Global.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/PersistNode.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/Menu.ts": {"mTimestamp": {"mtime": 1753501233577.879, "uuid": "5d5384ab-499f-4033-bbc9-ee1934c2d0c5"}, "chunkId": "b7458b4de0432717cd8478f412172b72032ccba8", "imports": [{"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 54}, "end": {"line": 1, "column": 58}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/PersistNode.ts": {"mTimestamp": {"mtime": 1753501233578.6836, "uuid": "402f76e8-a281-47a5-8c49-298291c49c53"}, "chunkId": "8aef75f8cf32d7d41c1396085863afa89f31c29e", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 82}, "end": {"line": 1, "column": 86}}}, {"value": "./factroy/AnimFactory", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 28}, "end": {"line": 2, "column": 51}}}, {"value": "./factroy/EnemyBulletFactory", "resolved": "__unresolved_2", "loc": {"start": {"line": 3, "column": 35}, "end": {"line": 3, "column": 65}}}, {"value": "./factroy/EnemyFactory", "resolved": "__unresolved_3", "loc": {"start": {"line": 4, "column": 29}, "end": {"line": 4, "column": 53}}}, {"value": "./factroy/GoodsFactory", "resolved": "__unresolved_4", "loc": {"start": {"line": 6, "column": 29}, "end": {"line": 6, "column": 53}}}, {"value": "./factroy/PlayerBulletFactory", "resolved": "__unresolved_5", "loc": {"start": {"line": 7, "column": 36}, "end": {"line": 7, "column": 67}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/factroy/AnimFactory.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/factroy/EnemyBulletFactory.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/factroy/EnemyFactory.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/factroy/GoodsFactory.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/factroy/PlayerBulletFactory.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/Player.ts": {"mTimestamp": {"mtime": 1753501233579.6875, "uuid": "53eed65f-04fc-48a2-9b7a-a716c1a966bc"}, "chunkId": "1963789e13e93dc0b226ee06945183fa9841c823", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 104}, "end": {"line": 1, "column": 108}}}, {"value": "./Global", "resolved": "__unresolved_1", "loc": {"start": {"line": 3, "column": 23}, "end": {"line": 3, "column": 33}}}, {"value": "./PersistNode", "resolved": "__unresolved_2", "loc": {"start": {"line": 4, "column": 28}, "end": {"line": 4, "column": 43}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/Global.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/PersistNode.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/PlayerBullet.ts": {"mTimestamp": {"mtime": 1753501233580.4856, "uuid": "5ac3438e-1947-45e8-b9b1-71747e7a1efd"}, "chunkId": "6f9d772ac3d8077fc881b5fdd9427f4a4c4d7eb2", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 171}, "end": {"line": 1, "column": 175}}}, {"value": "./Enemy", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 22}, "end": {"line": 2, "column": 31}}}, {"value": "./Global", "resolved": "__unresolved_2", "loc": {"start": {"line": 4, "column": 23}, "end": {"line": 4, "column": 33}}}, {"value": "./PersistNode", "resolved": "__unresolved_3", "loc": {"start": {"line": 5, "column": 28}, "end": {"line": 5, "column": 43}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/Enemy.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/Global.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/PersistNode.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/factroy/AnimFactory.ts": {"mTimestamp": {"mtime": 1753501233581.49, "uuid": "cdb002a1-a1b1-42c5-99bd-0032423f6753"}, "chunkId": "d95224f77434950531683a9785befc8381eb4ea1", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 57}, "end": {"line": 1, "column": 61}}}, {"value": "../PersistNode", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 28}, "end": {"line": 2, "column": 44}}}, {"value": "./GameFactory", "resolved": "__unresolved_2", "loc": {"start": {"line": 3, "column": 28}, "end": {"line": 3, "column": 43}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/PersistNode.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/factroy/GameFactory.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/factroy/EnemyBulletFactory.ts": {"mTimestamp": {"mtime": 1753501233582.224, "uuid": "82bdeebd-c187-4869-82f8-8a78ea38a888"}, "chunkId": "1a08f53eb63f00053ff256dfed4926f5de065ac5", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 57}, "end": {"line": 1, "column": 61}}}, {"value": "../EnemyBullet", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 28}, "end": {"line": 2, "column": 44}}}, {"value": "../Global", "resolved": "__unresolved_2", "loc": {"start": {"line": 3, "column": 23}, "end": {"line": 3, "column": 34}}}, {"value": "../PersistNode", "resolved": "__unresolved_3", "loc": {"start": {"line": 4, "column": 28}, "end": {"line": 4, "column": 44}}}, {"value": "./GameFactory", "resolved": "__unresolved_4", "loc": {"start": {"line": 5, "column": 28}, "end": {"line": 5, "column": 43}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/EnemyBullet.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/Global.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/PersistNode.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/factroy/GameFactory.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/factroy/EnemyFactory.ts": {"mTimestamp": {"mtime": 1753501233583.2283, "uuid": "11ec996f-30de-45ce-bb37-825678216d22"}, "chunkId": "b7e073d4f71adda5b81bb49903e8b28d4f91bd73", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 57}, "end": {"line": 1, "column": 61}}}, {"value": "../Enemy", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 22}, "end": {"line": 2, "column": 32}}}, {"value": "../Global", "resolved": "__unresolved_2", "loc": {"start": {"line": 3, "column": 23}, "end": {"line": 3, "column": 34}}}, {"value": "../PersistNode", "resolved": "__unresolved_3", "loc": {"start": {"line": 4, "column": 28}, "end": {"line": 4, "column": 44}}}, {"value": "./GameFactory", "resolved": "__unresolved_4", "loc": {"start": {"line": 5, "column": 28}, "end": {"line": 5, "column": 43}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/Enemy.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/Global.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/PersistNode.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/factroy/GameFactory.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/factroy/GameFactory.ts": {"mTimestamp": {"mtime": 1753501233583.963, "uuid": "7c1a95a6-c72a-48b2-82c7-dc072b10d00e"}, "chunkId": "aa6eb8cc91431ea77014b48fb3d674c1429f5b35", "imports": [{"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 60}, "end": {"line": 1, "column": 64}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/factroy/GoodsFactory.ts": {"mTimestamp": {"mtime": 1753501233584.967, "uuid": "6a38b65e-437d-4341-af31-e037c890fed3"}, "chunkId": "12a638bfa0ac053a36d52cbdf1de8f32a2b8e292", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 57}, "end": {"line": 1, "column": 61}}}, {"value": "../Global", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 23}, "end": {"line": 2, "column": 34}}}, {"value": "../Goods", "resolved": "__unresolved_2", "loc": {"start": {"line": 3, "column": 22}, "end": {"line": 3, "column": 32}}}, {"value": "../PersistNode", "resolved": "__unresolved_3", "loc": {"start": {"line": 4, "column": 28}, "end": {"line": 4, "column": 44}}}, {"value": "./GameFactory", "resolved": "__unresolved_4", "loc": {"start": {"line": 5, "column": 28}, "end": {"line": 5, "column": 43}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/Global.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/Goods.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/PersistNode.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/factroy/GameFactory.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/factroy/PlayerBulletFactory.ts": {"mTimestamp": {"mtime": 1753501233585.9304, "uuid": "73373683-639f-498e-a8c0-871383257cbe"}, "chunkId": "c6647226b8d9e9c0ee5733fb8979105406bf5cc1", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 57}, "end": {"line": 1, "column": 61}}}, {"value": "../Global", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 23}, "end": {"line": 2, "column": 34}}}, {"value": "../PersistNode", "resolved": "__unresolved_2", "loc": {"start": {"line": 3, "column": 28}, "end": {"line": 3, "column": 44}}}, {"value": "../PlayerBullet", "resolved": "__unresolved_3", "loc": {"start": {"line": 4, "column": 29}, "end": {"line": 4, "column": 46}}}, {"value": "./GameFactory", "resolved": "__unresolved_4", "loc": {"start": {"line": 5, "column": 28}, "end": {"line": 5, "column": 43}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/Global.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/PersistNode.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/PlayerBullet.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/factroy/GameFactory.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/level/core/LevelData.ts": {"mTimestamp": {"mtime": 1753501232978.1726, "uuid": "f74927ff-9ef4-4ad4-9eab-d346a22327b8"}, "chunkId": "5c6177446fd2faa8713cfff72c3ae83754ed0f4f", "imports": [{"value": "cc"}, {"value": "cc"}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/level/core/LevelSerializer.ts": {"mTimestamp": {"mtime": 1753501232981.965, "uuid": "5dedc284-0b4f-4ccc-b206-ceb4f9722bde"}, "chunkId": "323a8c5994f8b20e12f09cd4d2d8cd061836d514", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/level/core/Types.ts": {"mTimestamp": {"mtime": 1753501232981.965, "uuid": "df9194ea-9f3d-4045-b7ca-264b71eac88b"}, "chunkId": "046d807525505511ecf05655a8c20c5edadc3779", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 46}, "end": {"line": 1, "column": 50}}}, {"value": "./LevelData", "resolved": "__unresolved_1", "loc": {"start": {"line": 325, "column": 29}, "end": {"line": 325, "column": 42}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/level/core/LevelData.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/level/editor/EditorGizmos.ts": {"mTimestamp": {"mtime": 1753501233045.09, "uuid": "ef6e93b2-fcf7-4ce8-acd1-bf45f1df18ae"}, "chunkId": "0955e02420ac15ed40b8db54be054d4910d64a16", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 99}, "end": {"line": 1, "column": 103}}}, {"value": "../core/Types", "resolved": "__unresolved_1", "loc": {"start": {"line": 3, "column": 37}, "end": {"line": 3, "column": 52}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/level/core/Types.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/level/editor/LevelEditor.ts": {"mTimestamp": {"mtime": 1753502943495.434, "uuid": "01d18540-68c6-47a1-bbb5-61467b98c848"}, "chunkId": "f09c883c6761d0b2e61d95659bbef6664068e8b3", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 56}, "end": {"line": 1, "column": 60}}}, {"value": "../core/Types", "resolved": "__unresolved_1", "loc": {"start": {"line": 3, "column": 69}, "end": {"line": 3, "column": 84}}}, {"value": "../core/LevelSerializer", "resolved": "__unresolved_2", "loc": {"start": {"line": 4, "column": 55}, "end": {"line": 4, "column": 80}}}, {"value": "../runtime/LevelManager", "resolved": "__unresolved_3", "loc": {"start": {"line": 5, "column": 29}, "end": {"line": 5, "column": 54}}}, {"value": "../runtime/CameraManager", "resolved": "__unresolved_4", "loc": {"start": {"line": 6, "column": 30}, "end": {"line": 6, "column": 56}}}, {"value": "../runtime/PathManager", "resolved": "__unresolved_5", "loc": {"start": {"line": 7, "column": 28}, "end": {"line": 7, "column": 52}}}, {"value": "../runtime/SpawnerSystem", "resolved": "__unresolved_6", "loc": {"start": {"line": 8, "column": 30}, "end": {"line": 8, "column": 56}}}, {"value": "../runtime/MapSystem", "resolved": "__unresolved_7", "loc": {"start": {"line": 9, "column": 26}, "end": {"line": 9, "column": 48}}}, {"value": "../events/EventSystem", "resolved": "__unresolved_8", "loc": {"start": {"line": 10, "column": 34}, "end": {"line": 10, "column": 57}}}, {"value": "./EditorGizmos", "resolved": "__unresolved_9", "loc": {"start": {"line": 11, "column": 29}, "end": {"line": 11, "column": 45}}}, {"value": "./PathEditor", "resolved": "__unresolved_10", "loc": {"start": {"line": 12, "column": 27}, "end": {"line": 12, "column": 41}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/level/core/Types.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/level/core/LevelSerializer.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/LevelManager.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/CameraManager.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/PathManager.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/SpawnerSystem.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/MapSystem.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/level/events/EventSystem.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/level/editor/EditorGizmos.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/level/editor/PathEditor.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/level/editor/PathEditor.ts": {"mTimestamp": {"mtime": 1753501233047.094, "uuid": "9dc7fc2a-7a6e-47c4-a475-d714192e0745"}, "chunkId": "074300ecc0a1bae55cf24a4aec5415031cf84a44", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 103}, "end": {"line": 1, "column": 107}}}, {"value": "../core/Types", "resolved": "__unresolved_1", "loc": {"start": {"line": 3, "column": 37}, "end": {"line": 3, "column": 52}}}, {"value": "../runtime/PathManager", "resolved": "__unresolved_2", "loc": {"start": {"line": 4, "column": 28}, "end": {"line": 4, "column": 52}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/level/core/Types.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/PathManager.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/level/events/EventSystem.ts": {"mTimestamp": {"mtime": 1753501233066.702, "uuid": "ebd41303-70f0-4209-ad1f-f17396ebc8da"}, "chunkId": "f4d15e8644794a11a4a7e1c1dfa4b31d41d64117", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 57}, "end": {"line": 1, "column": 61}}}, {"value": "../core/Types", "resolved": "__unresolved_1", "loc": {"start": {"line": 3, "column": 56}, "end": {"line": 3, "column": 71}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/level/core/Types.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/CameraManager.ts": {"mTimestamp": {"mtime": 1753501233137.0144, "uuid": "06785874-c974-426e-be89-ba4067b4a8f3"}, "chunkId": "cc37ee62c4be7ae793f2051ec6537f09ddab9978", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 84}, "end": {"line": 1, "column": 88}}}, {"value": "../core/Types", "resolved": "__unresolved_1", "loc": {"start": {"line": 3, "column": 44}, "end": {"line": 3, "column": 59}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/level/core/Types.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/LevelManager.ts": {"mTimestamp": {"mtime": 1753501233138.1533, "uuid": "47600cae-087c-4c1f-8b45-624a94fecf8d"}, "chunkId": "0df2f44674d1ead79d50c4f9b190f10693a8e0a2", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 76}, "end": {"line": 1, "column": 80}}}, {"value": "../core/Types", "resolved": "__unresolved_1", "loc": {"start": {"line": 3, "column": 30}, "end": {"line": 3, "column": 45}}}, {"value": "./SubLevelComponent", "resolved": "__unresolved_2", "loc": {"start": {"line": 4, "column": 34}, "end": {"line": 4, "column": 55}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/level/core/Types.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/SubLevelComponent.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/MapLayerComponent.ts": {"mTimestamp": {"mtime": 1753501233139.3005, "uuid": "5eff8507-48b1-4399-83b5-1d5d5662a94a"}, "chunkId": "98868adf6b6d83f62e89581abd0546ea4fb0d2a2", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 107}, "end": {"line": 1, "column": 111}}}, {"value": "../core/Types", "resolved": "__unresolved_1", "loc": {"start": {"line": 3, "column": 28}, "end": {"line": 3, "column": 43}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/level/core/Types.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/MapSystem.ts": {"mTimestamp": {"mtime": 1753501233140.446, "uuid": "493225a7-a8b1-46fa-84b4-2416f862e324"}, "chunkId": "08adf0622c3a529112a521f23a0d5a02b6531aba", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 63}, "end": {"line": 1, "column": 67}}}, {"value": "../core/LevelData", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 39}, "end": {"line": 2, "column": 58}}}, {"value": "../core/Types", "resolved": "__unresolved_2", "loc": {"start": {"line": 3, "column": 41}, "end": {"line": 3, "column": 56}}}, {"value": "./MapLayerComponent", "resolved": "__unresolved_3", "loc": {"start": {"line": 4, "column": 34}, "end": {"line": 4, "column": 55}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/level/core/LevelData.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/level/core/Types.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/MapLayerComponent.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/PathFollower.ts": {"mTimestamp": {"mtime": 1753501233140.446, "uuid": "9761fbae-3b42-46c0-af28-24f077ad3ecb"}, "chunkId": "1cf9c3dbb012936a26c5aedda09409cec475901c", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 50}, "end": {"line": 1, "column": 54}}}, {"value": "../core/Types", "resolved": "__unresolved_1", "loc": {"start": {"line": 3, "column": 26}, "end": {"line": 3, "column": 41}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/level/core/Types.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/PathManager.ts": {"mTimestamp": {"mtime": 1753501233141.6362, "uuid": "e4d55c74-27c7-48f9-80b8-0e06c64d792b"}, "chunkId": "d344f5ec5a8c39d8d3d05db943d295ef5a3aabd1", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 57}, "end": {"line": 1, "column": 61}}}, {"value": "../core/Types", "resolved": "__unresolved_1", "loc": {"start": {"line": 3, "column": 39}, "end": {"line": 3, "column": 54}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/level/core/Types.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/SpawnerComponent.ts": {"mTimestamp": {"mtime": 1753501233142.7844, "uuid": "8c3d2dbc-7bce-4770-a6a3-4cff3848caf8"}, "chunkId": "5850102fe022728d9bd4ccaa9aa26f8c28ede683", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 82}, "end": {"line": 1, "column": 86}}}, {"value": "../core/Types", "resolved": "__unresolved_1", "loc": {"start": {"line": 3, "column": 40}, "end": {"line": 3, "column": 55}}}, {"value": "./PathFollower", "resolved": "__unresolved_2", "loc": {"start": {"line": 4, "column": 29}, "end": {"line": 4, "column": 45}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/level/core/Types.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/PathFollower.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/SpawnerSystem.ts": {"mTimestamp": {"mtime": 1753502413787.8794, "uuid": "adbfa057-f666-4e0b-86b3-e92e14d59d15"}, "chunkId": "7463a40fbec321b7410d64dfcdc1b3e6c62658cc", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 57}, "end": {"line": 1, "column": 61}}}, {"value": "../core/Types", "resolved": "__unresolved_1", "loc": {"start": {"line": 3, "column": 39}, "end": {"line": 3, "column": 54}}}, {"value": "./SpawnerComponent", "resolved": "__unresolved_2", "loc": {"start": {"line": 4, "column": 33}, "end": {"line": 4, "column": 53}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/level/core/Types.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/SpawnerComponent.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/SubLevelComponent.ts": {"mTimestamp": {"mtime": 1753501233145.1392, "uuid": "32e79263-806c-4849-8282-5ea416efb23b"}, "chunkId": "3ac4b7632cf277a4233524a4002d4cd334bbeaaf", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 64}, "end": {"line": 1, "column": 68}}}, {"value": "../core/Types", "resolved": "__unresolved_1", "loc": {"start": {"line": 3, "column": 41}, "end": {"line": 3, "column": 56}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/level/core/Types.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/TestComponent.ts": {"mTimestamp": {"mtime": 1753501233145.1392, "uuid": "b4ef553f-e7dc-4bd4-b3da-a1e1d1009bdd"}, "chunkId": "60b0742d864a6c139aedede62106ac6aad9d71ce", "imports": [{"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 44}, "end": {"line": 1, "column": 48}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/GameInstance.ts": {"mTimestamp": {"mtime": 1753501233599.695, "uuid": "815bc83a-9815-48da-859e-0a61c8d7764f"}, "chunkId": "6734c38c3959f7df5525dcbc49e6db21ed22087e", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 38}, "end": {"line": 1, "column": 42}}}, {"value": "./Luban/LubanMgr", "resolved": "__unresolved_1", "loc": {"start": {"line": 3, "column": 25}, "end": {"line": 3, "column": 43}}}, {"value": "./Network/NetMgr", "resolved": "__unresolved_2", "loc": {"start": {"line": 4, "column": 23}, "end": {"line": 4, "column": 41}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Luban/LubanMgr.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Network/NetMgr.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/IMgr.ts": {"mTimestamp": {"mtime": 1753501233600.3826, "uuid": "4f6cfd7a-7406-4eb5-a557-4e504e81c910"}, "chunkId": "c251e6d154e03d8cce491ae350d65095ae7f8c83", "imports": [{"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 44}, "end": {"line": 1, "column": 48}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Luban/LubanMgr.ts": {"mTimestamp": {"mtime": 1753501233601.3865, "uuid": "3d9905b2-c72b-4a59-84d9-ab422829ec3b"}, "chunkId": "17d3f4c55293eb211367452bdd4c17958b68e0ed", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 80}, "end": {"line": 1, "column": 84}}}, {"value": "../IMgr", "resolved": "__unresolved_1", "loc": {"start": {"line": 3, "column": 21}, "end": {"line": 3, "column": 30}}}, {"value": "../AutoGen/Luban/schema", "resolved": "__unresolved_2", "loc": {"start": {"line": 4, "column": 21}, "end": {"line": 4, "column": 46}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/IMgr.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/AutoGen/Luban/schema.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/MainUI.ts": {"mTimestamp": {"mtime": 1753501233611.2593, "uuid": "ae524ea8-f776-4444-9e5b-a5143e06dbf9"}, "chunkId": "a62bce7c65bfc0783f10bcc13fb7efd330e855ae", "imports": [{"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 55}, "end": {"line": 1, "column": 59}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Network/NetMgr.ts": {"mTimestamp": {"mtime": 1753501233612.0908, "uuid": "d7f90c8a-8a20-40f9-9a4a-98f31574e17a"}, "chunkId": "adac3babafbcaaff82b91cc080fe88b86548f97a", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 80}, "end": {"line": 1, "column": 84}}}, {"value": "../IMgr", "resolved": "__unresolved_1", "loc": {"start": {"line": 3, "column": 21}, "end": {"line": 3, "column": 30}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/IMgr.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/ResUpdate/ResUpdate.ts": {"mTimestamp": {"mtime": 1753501233618.68, "uuid": "30734ca9-59e8-4f53-9672-855d960d7427"}, "chunkId": "9d15e7d22d1a1328ffa5d899e41c0fefa70e2b52", "imports": [{"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 149}, "end": {"line": 1, "column": 153}}}, {"value": "cc/env", "loc": {"start": {"line": 2, "column": 23}, "end": {"line": 2, "column": 31}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cc/env"}, "messages": []}]}, "cce:/internal/code-quality/cr.mjs": {"mTimestamp": 1753512639407, "chunkId": "6a5019a719a9014c047e67aa1cf34453ab8392ce", "imports": [], "type": "esm", "resolutions": []}, "file:///E:/M2Game/Client/assets/scripts/Game/world/Bootstrap.ts": {"mTimestamp": {"mtime": 1753523190809.9175, "uuid": "65d59376-f39a-4365-8dd7-250c1d7140db"}, "chunkId": "a880040b201a43078251d202cec131aa4b75a57a", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 50}, "end": {"line": 1, "column": 54}}}, {"value": "./base/World", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 34}, "end": {"line": 2, "column": 48}}}, {"value": "./WorldInitializeData", "resolved": "__unresolved_2", "loc": {"start": {"line": 3, "column": 85}, "end": {"line": 3, "column": 108}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/world/base/World.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/world/WorldInitializeData.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/world/WorldInitializeData.ts": {"mTimestamp": {"mtime": 1753523094308.763, "uuid": "0c36c604-d0de-4dc7-9fc7-132803bb9bf9"}, "chunkId": "385bceab2f32443e2046001b74fb95c3888d001b", "imports": [{"value": "cc"}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/world/base/System.ts": {"mTimestamp": {"mtime": 1753523740028.965, "uuid": "68463dec-03c8-4483-a858-88be18da4675"}, "chunkId": "18115108ff656ee90e2863a8933b4707545ec1a5", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 27}, "end": {"line": 1, "column": 31}}}, {"value": "./TypeID", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 34}, "end": {"line": 2, "column": 44}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/world/base/TypeID.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/world/base/SystemContainer.ts": {"mTimestamp": {"mtime": 1753522890572.8606, "uuid": "237cafbd-51ce-4858-b19c-a058b48c9d0c"}, "chunkId": "da16bcc6584bc2ac43355704538ffd77fd8040f2", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 27}, "end": {"line": 1, "column": 31}}}, {"value": "./TypeID", "resolved": "__unresolved_1", "loc": {"start": {"line": 3, "column": 36}, "end": {"line": 3, "column": 46}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/world/base/TypeID.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/world/base/TypeID.ts": {"mTimestamp": {"mtime": 1753522752275.371, "uuid": "0eea3cf5-1ec0-4055-99d5-ee74c4c53850"}, "chunkId": "fa2784ebc0eff50dc40a64b60e09f681dd051e69", "imports": [{"value": "cc"}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/world/base/World.ts": {"mTimestamp": {"mtime": 1753523230453.3884, "uuid": "73f5b7e0-b6c4-4bd2-807b-a090d20b7c1c"}, "chunkId": "22fd948e93501951a69c80979846d28b25223745", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "./SystemContainer", "resolved": "__unresolved_1", "loc": {"start": {"line": 1, "column": 32}, "end": {"line": 1, "column": 51}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/world/base/SystemContainer.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/world/bullet/BulletSystem.ts": {"mTimestamp": {"mtime": 1753523712679.3455, "uuid": "7cb83a17-5595-4c04-9619-63b94934fc55"}, "chunkId": "89e87d2ae93ede041b1e31f4df93a413b6e36cc2", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 39}, "end": {"line": 1, "column": 43}}}, {"value": "../base/System", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 23}, "end": {"line": 2, "column": 39}}}, {"value": "../base/TypeID", "resolved": "__unresolved_2", "loc": {"start": {"line": 3, "column": 31}, "end": {"line": 3, "column": 47}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/world/base/System.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/world/base/TypeID.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/world/index.ts": {"mTimestamp": {"mtime": 1753523289675.1653, "uuid": "22545f8c-666f-4136-8361-0fe5fba57a0f"}, "chunkId": "930745d09e5696807f7ac7587de3ae2f72795c1d", "imports": [{"value": "cc"}, {"value": "./base/System", "resolved": "__unresolved_0", "loc": {"start": {"line": 9, "column": 23}, "end": {"line": 9, "column": 38}}}, {"value": "./base/SystemContainer", "resolved": "__unresolved_1", "loc": {"start": {"line": 10, "column": 32}, "end": {"line": 10, "column": 56}}}, {"value": "./base/TypeID", "resolved": "__unresolved_2", "loc": {"start": {"line": 12, "column": 50}, "end": {"line": 12, "column": 65}}}, {"value": "./base/TypeID", "resolved": "__unresolved_3", "loc": {"start": {"line": 14, "column": 28}, "end": {"line": 14, "column": 43}}}, {"value": "./base/World", "resolved": "__unresolved_4", "loc": {"start": {"line": 17, "column": 34}, "end": {"line": 17, "column": 48}}}, {"value": "./WorldInitializeData", "resolved": "__unresolved_5", "loc": {"start": {"line": 24, "column": 7}, "end": {"line": 24, "column": 30}}}, {"value": "./Bootstrap", "resolved": "__unresolved_6", "loc": {"start": {"line": 35, "column": 26}, "end": {"line": 35, "column": 39}}}, {"value": "./bullet/BulletSystem", "resolved": "__unresolved_7", "loc": {"start": {"line": 38, "column": 29}, "end": {"line": 38, "column": 52}}}, {"value": "./level/LevelSystem", "resolved": "__unresolved_8", "loc": {"start": {"line": 41, "column": 44}, "end": {"line": 41, "column": 65}}}, {"value": "./player/PlayerSystem", "resolved": "__unresolved_9", "loc": {"start": {"line": 49, "column": 42}, "end": {"line": 49, "column": 65}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/world/base/System.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/world/base/SystemContainer.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/world/base/TypeID.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/world/base/TypeID.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/world/base/World.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/world/WorldInitializeData.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/world/Bootstrap.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/world/bullet/BulletSystem.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/world/level/LevelSystem.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/world/player/PlayerSystem.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/world/level/LevelSystem.ts": {"mTimestamp": {"mtime": 1753522973312.641, "uuid": "21f782d5-044f-45eb-b25d-c6291183b1ad"}, "chunkId": "c48055c8729e2ddd6c65e69f10e6be25f855116b", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 39}, "end": {"line": 1, "column": 43}}}, {"value": "../base/System", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 23}, "end": {"line": 2, "column": 39}}}, {"value": "../base/TypeID", "resolved": "__unresolved_2", "loc": {"start": {"line": 3, "column": 31}, "end": {"line": 3, "column": 47}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/world/base/System.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/world/base/TypeID.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/world/player/PlayerSystem.ts": {"mTimestamp": {"mtime": 1753523650008.387, "uuid": "4fec5f67-8da2-438f-9ffc-6732e3de3cf0"}, "chunkId": "34551532144943d5b236508c5ff8c0f8cd4ba4f3", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "resolved": "__unresolved_0"}, {"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 39}, "end": {"line": 1, "column": 43}}}, {"value": "../base/System", "resolved": "__unresolved_1", "loc": {"start": {"line": 2, "column": 23}, "end": {"line": 2, "column": 39}}}, {"value": "../base/TypeID", "resolved": "__unresolved_2", "loc": {"start": {"line": 3, "column": 31}, "end": {"line": 3, "column": 47}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/code-quality/cr.mjs"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/world/base/System.ts"}, "messages": []}, {"resolved": {"type": "module", "url": "file:///E:/M2Game/Client/assets/scripts/Game/world/base/TypeID.ts"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/world/bullet/Emitter.ts": {"mTimestamp": {"mtime": 1753526762872.3447, "uuid": "2564d02b-7111-4a64-aa28-de874242b1f0"}, "chunkId": "eb065a22717f447f6d2a5623f6f7fdb1b64865a1", "imports": [{"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 59}, "end": {"line": 1, "column": 63}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/scripts/Game/world/bullet/Bullet.ts": {"mTimestamp": {"mtime": 1753523940083.7605, "uuid": "21904ac5-11f3-4245-b818-c752c4f8ce8f"}, "chunkId": "e486c6d3a19894b9b42ded541c8d00f19e609286", "imports": [{"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 44}, "end": {"line": 1, "column": 48}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/gizmos/Emitter.ts": {"mTimestamp": {"mtime": 1753524596363.933, "uuid": "b94b54e2-e54c-4c15-9235-2115d8ba8651"}, "chunkId": "929763e07631558fcf7acf42a592befb34df2638", "imports": [{"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 7, "column": 28}, "end": {"line": 7, "column": 32}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}]}, "file:///E:/M2Game/Client/assets/gizmos/EmitterGizmo.ts": {"mTimestamp": {"mtime": 1753526098460.2927, "uuid": "92b822be-e838-4774-8286-851dc74ce5af"}, "chunkId": "b37c1a13f611ddf8b31757285f1055a41cc2342e", "imports": [{"value": "cc"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 7, "column": 55}, "end": {"line": 7, "column": 59}}}, {"value": "cc/env", "loc": {"start": {"line": 8, "column": 23}, "end": {"line": 8, "column": 31}}}], "type": "esm", "resolutions": [{"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "module", "url": "cce:/internal/x/cc"}, "messages": []}, {"resolved": {"type": "external", "specifierOrURL": "cc/env"}, "messages": []}]}}}