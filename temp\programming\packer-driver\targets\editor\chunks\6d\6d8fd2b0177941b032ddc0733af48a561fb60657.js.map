{"version": 3, "sources": ["cce:/internal/x/prerequisite-imports"], "names": ["requests", "request", "_err"], "mappings": ";;;;;;AACA;AAEA,YAAM,CAAC,YAAY;AACf,cAAMA,QAAQ,GAAG,CAAC,uCAAD,EAA4J,uCAA5J,EAA4T,uCAA5T,EAAge,uCAAhe,EAAioB,uCAAjoB,EAA4xB,uCAA5xB,EAAg7B,uCAAh7B,EAAw/B,uCAAx/B,EAAykC,uCAAzkC,EAAypC,uCAAzpC,EAA+tC,wCAA/tC,EAA2yC,wCAA3yC,EAAk3C,wCAAl3C,EAA+7C,wCAA/7C,EAAygD,wCAAzgD,EAAilD,wCAAjlD,EAAwpD,wCAAxpD,EAAkuD,wCAAluD,EAAwyD,wCAAxyD,EAAq3D,wCAAr3D,EAA67D,wCAA77D,EAA2gE,wCAA3gE,EAAgmE,wCAAhmE,EAA4rE,wCAA5rE,EAAkxE,wCAAlxE,EAAu2E,wCAAv2E,EAA67E,wCAA77E,EAA0hF,wCAA1hF,EAA2mF,wCAA3mF,EAAssF,wCAAtsF,EAAyxF,wCAAzxF,EAAq3F,wCAAr3F,EAAw8F,wCAAx8F,EAA0hG,wCAA1hG,EAA+mG,wCAA/mG,EAA0sG,wCAA1sG,EAAgyG,wCAAhyG,EAA62G,wCAA72G,EAAs8G,wCAAt8G,EAAiiH,wCAAjiH,EAA0mH,wCAA1mH,EAA2qH,wCAA3qH,EAAsvH,wCAAtvH,EAAyzH,wCAAzzH,EAAo4H,wCAAp4H,CAAjB;;AACA,aAAK,MAAMC,OAAX,IAAsBD,QAAtB,EAAgC;AAC5B,cAAI;AACA,kBAAMC,OAAO,EAAb;AACH,WAFD,CAEE,OAAOC,IAAP,EAAa,CACX;AACH;AACJ;AACJ,OATK,GAAN", "sourcesContent": ["\n// Auto generated represents the prerequisite imports of project modules.\n\nawait (async () => {\n    const requests = [() => import(\"file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts\"), () => import(\"file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts\"), () => import(\"file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts\"), () => import(\"file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts\"), () => import(\"file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts\"), () => import(\"file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts\"), () => import(\"file:///E:/M2Game/Client/assets/gizmos/EmitterGizmo.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/AutoGen/Luban/schema.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/AutoGen/PB/cs_proto.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/Anim.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/Background.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/Enemy.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/EnemyBullet.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/GameOver.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/Global.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/Goods.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/MainGame.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/Menu.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/PersistNode.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/Player.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/PlayerBullet.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/factroy/AnimFactory.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/factroy/EnemyBulletFactory.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/factroy/EnemyFactory.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/factroy/GameFactory.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/factroy/GoodsFactory.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/factroy/PlayerBulletFactory.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/world/Bootstrap.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/world/WorldInitializeData.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/world/base/System.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/world/base/SystemContainer.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/world/base/TypeID.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/world/base/World.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/world/bullet/Bullet.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/world/bullet/BulletSystem.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/world/bullet/Emitter.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/world/index.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/world/level/LevelSystem.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/world/player/PlayerSystem.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameInstance.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/IMgr.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Luban/LubanMgr.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/MainUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Network/NetMgr.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/ResUpdate/ResUpdate.ts\")];\n    for (const request of requests) {\n        try {\n            await request();\n        } catch (_err) {\n            // The error should have been caught by executor.\n        }\n    }\n})();\n    "]}