{"version": 3, "sources": ["cce:/internal/x/prerequisite-imports"], "names": ["requests", "request", "_err"], "mappings": ";;;;;;AACA;AAEA,YAAM,CAAC,YAAY;AACf,cAAMA,QAAQ,GAAG,CAAC,uCAAD,EAA4J,uCAA5J,EAA4T,uCAA5T,EAAge,uCAAhe,EAAioB,uCAAjoB,EAA4xB,uCAA5xB,EAAg7B,uCAAh7B,EAAm/B,uCAAn/B,EAAokC,uCAApkC,EAAopC,uCAAppC,EAA0tC,wCAA1tC,EAAsyC,wCAAtyC,EAA62C,wCAA72C,EAA07C,wCAA17C,EAAogD,wCAApgD,EAA4kD,wCAA5kD,EAAmpD,wCAAnpD,EAA6tD,wCAA7tD,EAAmyD,wCAAnyD,EAAg3D,wCAAh3D,EAAw7D,wCAAx7D,EAAsgE,wCAAtgE,EAA2lE,wCAA3lE,EAAurE,wCAAvrE,EAA6wE,wCAA7wE,EAAk2E,wCAAl2E,EAAw7E,wCAAx7E,EAAqhF,wCAArhF,EAA2mF,wCAA3mF,EAAusF,wCAAvsF,EAAyxF,wCAAzxF,EAAo3F,wCAAp3F,EAA88F,wCAA98F,EAAuiG,wCAAviG,EAAioG,wCAAjoG,EAA8tG,wCAA9tG,EAA0zG,wCAA1zG,EAA25G,wCAA35G,EAAo/G,wCAAp/G,EAAglH,wCAAhlH,EAA2qH,wCAA3qH,EAA2wH,wCAA3wH,EAAw2H,wCAAx2H,EAAy8H,wCAAz8H,EAA0hI,wCAA1hI,EAAqnI,wCAArnI,EAAwsI,wCAAxsI,EAAoyI,wCAApyI,EAAu3I,wCAAv3I,EAAy8I,wCAAz8I,EAA8hJ,wCAA9hJ,EAAynJ,wCAAznJ,EAA+sJ,wCAA/sJ,EAA4xJ,wCAA5xJ,EAAq3J,wCAAr3J,EAAg9J,wCAAh9J,EAAyhK,wCAAzhK,EAA0lK,wCAA1lK,EAAqqK,wCAArqK,EAAwuK,wCAAxuK,EAAmzK,wCAAnzK,CAAjB;;AACA,aAAK,MAAMC,OAAX,IAAsBD,QAAtB,EAAgC;AAC5B,cAAI;AACA,kBAAMC,OAAO,EAAb;AACH,WAFD,CAEE,OAAOC,IAAP,EAAa,CACX;AACH;AACJ;AACJ,OATK,GAAN", "sourcesContent": ["\n// Auto generated represents the prerequisite imports of project modules.\n\nawait (async () => {\n    const requests = [() => import(\"file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts\"), () => import(\"file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts\"), () => import(\"file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts\"), () => import(\"file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts\"), () => import(\"file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts\"), () => import(\"file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts\"), () => import(\"file:///E:/M2Game/Client/assets/gizmos/Emitter.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/AutoGen/Luban/schema.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/AutoGen/PB/cs_proto.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/Anim.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/Background.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/Enemy.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/EnemyBullet.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/GameOver.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/Global.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/Goods.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/MainGame.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/Menu.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/PersistNode.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/Player.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/PlayerBullet.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/factroy/AnimFactory.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/factroy/EnemyBulletFactory.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/factroy/EnemyFactory.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/factroy/GameFactory.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/factroy/GoodsFactory.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/factroy/PlayerBulletFactory.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/level/core/LevelData.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/level/core/LevelSerializer.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/level/core/Types.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/level/editor/EditorGizmos.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/level/editor/LevelEditor.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/level/editor/PathEditor.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/level/events/EventSystem.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/CameraManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/LevelManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/MapLayerComponent.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/MapSystem.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/PathFollower.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/PathManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/SpawnerComponent.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/SpawnerSystem.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/level/runtime/SubLevelComponent.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/world/Bootstrap.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/world/WorldInitializeData.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/world/base/System.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/world/base/SystemContainer.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/world/base/TypeID.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/world/base/World.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/world/bullet/Bullet.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/world/bullet/BulletSystem.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/world/bullet/Emitter.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/world/index.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/world/level/LevelSystem.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/world/player/PlayerSystem.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameInstance.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/IMgr.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Luban/LubanMgr.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/MainUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Network/NetMgr.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/ResUpdate/ResUpdate.ts\")];\n    for (const request of requests) {\n        try {\n            await request();\n        } catch (_err) {\n            // The error should have been caught by executor.\n        }\n    }\n})();\n    "]}