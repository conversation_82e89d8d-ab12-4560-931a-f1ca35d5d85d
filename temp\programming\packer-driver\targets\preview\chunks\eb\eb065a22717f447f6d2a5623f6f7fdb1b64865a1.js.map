{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/world/bullet/Emitter.ts"], "names": ["_decorator", "Component", "Node", "ccclass", "property", "Emitter", "type", "isActive", "canTrigger", "setActive", "active", "schedule", "emitBullet", "frequency", "unschedule", "getDirection", "index", "angleOffset", "arc", "count", "radian", "angle", "Math", "PI", "x", "cos", "y", "sin"], "mappings": ";;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;;;;;;;;;OAC1B;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBJ,U;;yBAGjBK,O,WADZF,OAAO,CAAC,SAAD,C,UAGHC,QAAQ,CAAC;AAACE,QAAAA,IAAI,EAAEJ;AAAP,OAAD,C,2BAHb,MACaG,OADb,SAC6BJ,SAD7B,CACuC;AAAA;AAAA;;AAAA;;AAKnC;AALmC;;AASnC;AATmC;;AAanC;AAbmC;;AAiBnC;AAjBmC;;AAqBnC;AArBmC;;AAyBnC;AAzBmC;;AAAA,eA6BnCM,QA7BmC,GA6Bf,KA7Be;AAAA;;AA+BnCC,QAAAA,UAAU,GAAY;AAClB;AACA,iBAAO,KAAP;AACH;;AAEDC,QAAAA,SAAS,CAACC,MAAD,EAAwB;AAC7B,eAAKH,QAAL,GAAgBG,MAAhB;;AACA,cAAIA,MAAJ,EAAY;AACR,iBAAKC,QAAL,CAAc,KAAKC,UAAnB,EAA+B,KAAKC,SAApC;AACH,WAFD,MAEO;AACH,iBAAKC,UAAL,CAAgB,KAAKF,UAArB;AACH;AACJ;;AAEDA,QAAAA,UAAU,GAAS,CACf;AAEH;;AAEDG,QAAAA,YAAY,CAACC,KAAD,EAA0C;AAClD;AACA,cAAMC,WAAW,GAAI,KAAKC,GAAL,IAAY,KAAKC,KAAL,GAAa,CAAzB,CAAD,GAAgCH,KAAhC,GAAwC,KAAKE,GAAL,GAAW,CAAvE;AACA,cAAME,MAAM,GAAG,CAAC,KAAKC,KAAL,GAAaJ,WAAd,KAA8BK,IAAI,CAACC,EAAL,GAAU,GAAxC,CAAf;AACA,iBAAO;AACHC,YAAAA,CAAC,EAAEF,IAAI,CAACG,GAAL,CAASL,MAAT,CADA;AAEHM,YAAAA,CAAC,EAAEJ,IAAI,CAACK,GAAL,CAASP,MAAT;AAFA,WAAP;AAIH;;AA1DkC,O;;;;;iBAGd,I;;gFAGpBhB,Q;;;;;iBACe,E;;8EAGfA,Q;;;;;iBACa,C;;iFAGbA,Q;;;;;iBACgB,C;;gFAGhBA,Q;;;;;iBACe,C;;gFAGfA,Q;;;;;iBACe,C;;oFAGfA,Q;;;;;iBACmB,C", "sourcesContent": ["import { _decorator, Component, Node } from 'cc';\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('Emitter')\r\nexport class Emitter extends Component {\r\n\r\n    @property({type: Node})\r\n    bulletPrefab: Node = null;\r\n\r\n    // 发射角度\r\n    @property\r\n    angle: number = 90;\r\n\r\n    // 发射弧度\r\n    @property\r\n    arc: number = 0;\r\n\r\n    // 发射半径\r\n    @property\r\n    radius: number = 0;\r\n\r\n    // 发射条数\r\n    @property\r\n    count: number = 1;\r\n\r\n    // 子弹初速度\r\n    @property\r\n    speed: number = 1;\r\n\r\n    // 频率(间隔多少秒发射一次)\r\n    @property\r\n    frequency: number = 1;\r\n\r\n    isActive: boolean = false;\r\n\r\n    canTrigger(): boolean {\r\n        // 检查是否可以触发发射\r\n        return false;\r\n    }\r\n\r\n    setActive(active: boolean): void {\r\n        this.isActive = active;\r\n        if (active) {\r\n            this.schedule(this.emitBullet, this.frequency);\r\n        } else {\r\n            this.unschedule(this.emitBullet);\r\n        }\r\n    }\r\n\r\n    emitBullet(): void {\r\n        // 发射子弹\r\n\r\n    }\r\n\r\n    getDirection(index: number): { x: number, y: number } {\r\n        // 计算发射方向\r\n        const angleOffset = (this.arc / (this.count - 1)) * index - this.arc / 2;\r\n        const radian = (this.angle + angleOffset) * (Math.PI / 180);\r\n        return {\r\n            x: Math.cos(radian),\r\n            y: Math.sin(radian)\r\n        };\r\n    }\r\n}\r\n"]}