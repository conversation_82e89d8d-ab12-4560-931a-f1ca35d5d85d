{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/world/bullet/Emitter.ts"], "names": ["_decorator", "Component", "Node", "Camera", "Color", "ccclass", "property", "executeInEditMode", "Emitter", "type", "isActive", "canTrigger", "setActive", "active", "schedule", "emitBullet", "frequency", "unschedule", "update", "deltaTime", "mainCamera", "node", "scene", "getComponentInChildren", "camera", "<PERSON><PERSON><PERSON><PERSON>", "addCircle", "worldPosition", "GREEN"], "mappings": ";;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,K,OAAAA,K;;;;;;;;;OACxC;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,QAAX;AAAqBC,QAAAA;AAArB,O,GAA2CP,U;;yBAIpCQ,O,WAFZH,OAAO,CAAC,SAAD,C,UACPE,iBAAiB,CAAC,IAAD,C,UAGbD,QAAQ,CAAC;AAACG,QAAAA,IAAI,EAAEP;AAAP,OAAD,C,0CAJb,MAEaM,OAFb,SAE6BP,SAF7B,CAEuC;AAAA;AAAA;;AAAA;;AAKnC;AALmC;;AASnC;AATmC;;AAanC;AAbmC;;AAiBnC;AAjBmC;;AAqBnC;AArBmC;;AAAA,eAyBnCS,QAzBmC,GAyBf,KAzBe;AAAA;;AA2BnCC,QAAAA,UAAU,GAAY;AAClB;AACA,iBAAO,KAAP;AACH;;AAEDC,QAAAA,SAAS,CAACC,MAAD,EAAwB;AAC7B,eAAKH,QAAL,GAAgBG,MAAhB;;AACA,cAAIA,MAAJ,EAAY;AACR,iBAAKC,QAAL,CAAc,KAAKC,UAAnB,EAA+B,KAAKC,SAApC;AACH,WAFD,MAEO;AACH,iBAAKC,UAAL,CAAgB,KAAKF,UAArB;AACH;AACJ;;AAEDA,QAAAA,UAAU,GAAS,CACf;AAEH;;AAEDG,QAAAA,MAAM,CAACC,SAAD,EAAoB;AAAA;;AACtB,cAAMC,UAAU,GAAG,KAAKC,IAAL,CAAUC,KAAV,CAAgBC,sBAAhB,CAAuCpB,MAAvC,CAAnB;AACAiB,UAAAA,UAAU,QAAV,0BAAAA,UAAU,CAAEI,MAAZ,sDAAoBC,gBAApB,gCAAsCC,SAAtC,CAAgD,KAAKL,IAAL,CAAUM,aAA1D,EAAyE,CAAzE,EAA4EvB,KAAK,CAACwB,KAAlF,EAAyF,EAAzF;AACH;;AAjDkC,O;;;;;iBAGd,I;;gFAGpBtB,Q;;;;;iBACe,E;;iFAGfA,Q;;;;;iBACgB,C;;gFAGhBA,Q;;;;;iBACe,C;;gFAGfA,Q;;;;;iBACe,C;;oFAGfA,Q;;;;;iBACmB,C", "sourcesContent": ["import { _decorator, Component, Node, Camera, Color } from 'cc';\r\nconst { ccclass, property, executeInEditMode } = _decorator;\r\n\r\n@ccclass('Emitter')\r\n@executeInEditMode(true)\r\nexport class Emitter extends Component {\r\n\r\n    @property({type: Node})\r\n    bulletPrefab: Node = null;\r\n\r\n    // 发射角度\r\n    @property\r\n    angle: number = 90;\r\n\r\n    // 发射半径\r\n    @property\r\n    radius: number = 0;\r\n\r\n    // 发射条数\r\n    @property\r\n    count: number = 1;\r\n\r\n    // 子弹初速度\r\n    @property\r\n    speed: number = 1;\r\n\r\n    // 频率(间隔多少秒发射一次)\r\n    @property\r\n    frequency: number = 1;\r\n\r\n    isActive: boolean = false;\r\n\r\n    canTrigger(): boolean {\r\n        // 检查是否可以触发发射\r\n        return false;\r\n    }\r\n\r\n    setActive(active: boolean): void {\r\n        this.isActive = active;\r\n        if (active) {\r\n            this.schedule(this.emitBullet, this.frequency);\r\n        } else {\r\n            this.unschedule(this.emitBullet);\r\n        }\r\n    }\r\n\r\n    emitBullet(): void {\r\n        // 发射子弹\r\n\r\n    }\r\n\r\n    update(deltaTime: number) {\r\n        const mainCamera = this.node.scene.getComponentInChildren(Camera);\r\n        mainCamera?.camera?.geometryRenderer?.addCircle(this.node.worldPosition, 1, Color.GREEN, 20);\r\n    }\r\n}\r\n"]}