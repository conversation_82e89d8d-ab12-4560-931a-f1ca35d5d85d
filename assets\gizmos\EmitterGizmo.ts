/**
 * Emitter Gizmo Component
 * This component provides visual debugging for Emitter components in the scene view
 * It should be added to the same node as the Emitter component
 */

import { _decorator, Component, Color, Graphics } from 'cc';
import { EDITOR } from 'cc/env';
const { ccclass, property, executeInEditMode } = _decorator;

@ccclass('EmitterGizmo')
@executeInEditMode(true)
export class EmitterGizmo extends Component {

    @property
    public showRadius: boolean = true;

    @property
    public showDirections: boolean = true;

    @property
    public showCenter: boolean = true;

    @property
    public showArc: boolean = true;

    @property
    public radiusColor: Color = Color.GRAY;

    @property
    public directionColor: Color = Color.RED;

    @property
    public centerColor: Color = Color.WHITE;

    @property
    public arcColor: Color = Color.YELLOW;

    @property
    public speedScale: number = 1.0;

    private graphics: Graphics | null = null;
    private emitter: any = null;

    protected onLoad(): void {
        if (!EDITOR) return;

        // Get or create Graphics component
        this.graphics = this.getComponent(Graphics) || this.addComponent(Graphics);

        // Get Emitter component
        this.emitter = this.getComponent('Emitter');

        if (!this.emitter) {
            console.warn('EmitterGizmo: No Emitter component found on this node');
        }
    }

    protected update(): void {
        if (!EDITOR || !this.graphics || !this.emitter) return;

        this.drawGizmos();
    }

    private drawGizmos(): void {
        if (!this.graphics) return;

        // Clear previous drawings
        this.graphics.clear();

        // Draw center point
        if (this.showCenter) {
            this.drawCenter();
        }

        // Draw radius circle
        if (this.showRadius && this.emitter.radius > 0) {
            this.drawRadius();
        }

        // Draw arc visualization
        if (this.showArc && this.emitter.arc > 0) {
            this.drawArc();
        }

        // Draw direction arrows
        if (this.showDirections) {
            this.drawDirections();
        }
    }

    private drawCenter(): void {
        if (!this.graphics) return;

        this.graphics.strokeColor = this.centerColor;
        this.graphics.lineWidth = 2;

        const centerSize = 8;

        // Draw cross at center
        this.graphics.moveTo(-centerSize, 0);
        this.graphics.lineTo(centerSize, 0);
        this.graphics.moveTo(0, -centerSize);
        this.graphics.lineTo(0, centerSize);
        this.graphics.stroke();
    }

    private drawRadius(): void {
        if (!this.graphics) return;

        this.graphics.strokeColor = this.radiusColor;
        this.graphics.lineWidth = 1;

        // Draw radius circle
        this.graphics.circle(0, 0, this.emitter.radius);
        this.graphics.stroke();
    }

    private drawArc(): void {
        if (!this.graphics) return;

        this.graphics.strokeColor = this.arcColor;
        this.graphics.lineWidth = 2;

        const baseDirection = this.emitter.angle || 0;
        const totalArc = this.emitter.arc || 0;
        const arcRadius = Math.max(this.emitter.radius + 20, 50); // Arc drawn outside radius

        // Calculate start and end angles for the arc
        const startAngle = (baseDirection - totalArc / 2 + 90) * Math.PI / 180; // +90 for Cocos coordinate system
        const endAngle = (baseDirection + totalArc / 2 + 90) * Math.PI / 180;

        // Draw arc using line segments
        const segments = Math.max(8, Math.floor(totalArc / 5)); // More segments for larger arcs
        const angleStep = (endAngle - startAngle) / segments;

        for (let i = 0; i < segments; i++) {
            const angle1 = startAngle + angleStep * i;
            const angle2 = startAngle + angleStep * (i + 1);

            const x1 = Math.cos(angle1) * arcRadius;
            const y1 = Math.sin(angle1) * arcRadius;
            const x2 = Math.cos(angle2) * arcRadius;
            const y2 = Math.sin(angle2) * arcRadius;

            this.graphics.moveTo(x1, y1);
            this.graphics.lineTo(x2, y2);
        }

        // Draw arc end markers
        const markerSize = 8;
        const startX = Math.cos(startAngle) * arcRadius;
        const startY = Math.sin(startAngle) * arcRadius;
        const endX = Math.cos(endAngle) * arcRadius;
        const endY = Math.sin(endAngle) * arcRadius;

        // Start marker
        this.graphics.moveTo(startX - markerSize, startY);
        this.graphics.lineTo(startX + markerSize, startY);
        this.graphics.moveTo(startX, startY - markerSize);
        this.graphics.lineTo(startX, startY + markerSize);

        // End marker
        this.graphics.moveTo(endX - markerSize, endY);
        this.graphics.lineTo(endX + markerSize, endY);
        this.graphics.moveTo(endX, endY - markerSize);
        this.graphics.lineTo(endX, endY + markerSize);

        this.graphics.stroke();
    }

    private drawDirections(): void {
        if (!this.graphics || this.emitter.count <= 0) return;

        this.graphics.strokeColor = this.directionColor;
        this.graphics.lineWidth = 2;

        // Use arc property for spread calculation, angle for direction
        const baseDirection = this.emitter.angle || 0; // Base direction from angle property
        const totalArc = this.emitter.arc || 0; // Total arc to spread bullets across

        // Calculate angle per bullet based on arc and count
        const anglePerBullet = this.emitter.count > 1 ? totalArc / (this.emitter.count - 1) : 0;
        const startAngle = baseDirection - totalArc / 2; // Start from base direction minus half arc

        for (let i = 0; i < this.emitter.count; i++) {
            let bulletAngle: number;

            if (this.emitter.count === 1) {
                bulletAngle = baseDirection; // Single bullet goes in base direction
            } else {
                bulletAngle = startAngle + (anglePerBullet * i);
            }

            // Convert angle to radians (0 degrees = up in Cocos Creator)
            const angleRad = (bulletAngle + 90) * Math.PI / 180;

            // Calculate direction vector
            const dirX = Math.cos(angleRad);
            const dirY = Math.sin(angleRad);

            // Start position (at radius distance from center)
            const startX = dirX * this.emitter.radius;
            const startY = dirY * this.emitter.radius;

            // Calculate arrow length based on speed factor
            // Base length of 30 pixels, scaled by speed factor and speedScale property
            const baseLength = 30;
            const speedFactor = this.emitter.speed || 1; // Default to 1 if speed is 0 or undefined
            const arrowLength = Math.max(baseLength, baseLength * speedFactor * this.speedScale);

            const endX = startX + dirX * arrowLength;
            const endY = startY + dirY * arrowLength;

            // Draw arrow line
            this.graphics.moveTo(startX, startY);
            this.graphics.lineTo(endX, endY);

            // Draw arrow head
            this.drawArrowHead(endX, endY, dirX, dirY);
        }

        this.graphics.stroke();
    }

    private drawArrowHead(endX: number, endY: number, dirX: number, dirY: number): void {
        if (!this.graphics) return;

        const arrowSize = 8;

        // Calculate arrow head points
        const arrowAngle = Math.PI / 6; // 30 degrees

        // Left arrow point
        const leftX = endX - arrowSize * (dirX * Math.cos(arrowAngle) - dirY * Math.sin(arrowAngle));
        const leftY = endY - arrowSize * (dirY * Math.cos(arrowAngle) + dirX * Math.sin(arrowAngle));

        // Right arrow point
        const rightX = endX - arrowSize * (dirX * Math.cos(-arrowAngle) - dirY * Math.sin(-arrowAngle));
        const rightY = endY - arrowSize * (dirY * Math.cos(-arrowAngle) + dirX * Math.sin(-arrowAngle));

        // Draw arrow head lines
        this.graphics.moveTo(endX, endY);
        this.graphics.lineTo(leftX, leftY);
        this.graphics.moveTo(endX, endY);
        this.graphics.lineTo(rightX, rightY);
    }
}
