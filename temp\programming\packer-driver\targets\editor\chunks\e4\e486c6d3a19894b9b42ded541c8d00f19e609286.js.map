{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/world/bullet/Bullet.ts"], "names": ["_decorator", "Component", "ccclass", "property", "Bullet", "update", "deltaTime"], "mappings": ";;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;;;;;;;;;OACf;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBH,U;;wBAGjBI,M,WADZF,OAAO,CAAC,QAAD,C,gBAAR,MACaE,MADb,SAC4BH,SAD5B,CACsC;AAIlCI,QAAAA,MAAM,CAACC,SAAD,EAAoB,CAEzB;;AANiC,O", "sourcesContent": ["import { _decorator, Component, Node } from 'cc';\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('Bullet')\r\nexport class Bullet extends Component {\r\n\r\n    \r\n\r\n    update(deltaTime: number) {\r\n        \r\n    }\r\n}\r\n\r\n\r\n"]}