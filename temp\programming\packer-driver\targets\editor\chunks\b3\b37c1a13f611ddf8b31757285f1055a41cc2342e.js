System.register(["cc"], function (_export, _context) {
  "use strict";

  var _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, Vec3, Color, _crd, EmitterGizmo;

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      Vec3 = _cc.Vec3;
      Color = _cc.Color;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "92b82K+6DhHdIKGhR3HTOWv", "EmitterGizmo", undefined);
      /**
       * Gizmo for Emitter component
       * This file should be placed in the assets/gizmos/ folder
       * to be automatically recognized by Cocos Creator editor
       */


      // Gizmo definition for Emitter component
      __checkObsolete__(['Vec3', 'Color']);

      EmitterGizmo = {
        /**
         * Called when drawing gizmos for the Emitter component
         * @param target The Emitter component instance
         * @param gizmos The gizmos drawing API
         */
        onDrawGizmos(target, gizmos) {
          if (!target.node) return; // Get world position

          const worldPos = target.node.worldPosition; // Draw emission radius circle (optional, for reference)

          if (target.radius > 0) {
            gizmos.color = Color.GRAY;
            gizmos.strokeWidth = 1;
            gizmos.circle(worldPos, target.radius);
          } // Calculate angle per bullet


          const anglePerBullet = target.count > 1 ? target.angle / (target.count - 1) : 0;
          const startAngle = -target.angle / 2; // Start from negative half angle
          // Draw emission arc

          if (target.angle > 0 && target.radius > 0) {
            gizmos.color = Color.YELLOW;
            gizmos.strokeWidth = 2;
            this.drawArc(gizmos, worldPos, target.radius, startAngle, target.angle, 32);
          } // Draw bullet direction arrows


          gizmos.color = Color.RED;
          gizmos.strokeWidth = 2;

          for (let i = 0; i < target.count; i++) {
            let bulletAngle;

            if (target.count === 1) {
              bulletAngle = 0; // Single bullet goes straight
            } else {
              bulletAngle = startAngle + anglePerBullet * i;
            } // Convert angle to radians


            const angleRad = bulletAngle * Math.PI / 180; // Calculate direction vector

            const dirX = Math.cos(angleRad);
            const dirY = Math.sin(angleRad); // Start position (at radius distance from center)

            const startPos = new Vec3(worldPos.x + dirX * target.radius, worldPos.y + dirY * target.radius, worldPos.z); // End position (arrow length)

            const arrowLength = Math.max(50, target.radius * 0.5);
            const endPos = new Vec3(startPos.x + dirX * arrowLength, startPos.y + dirY * arrowLength, startPos.z); // Draw arrow line

            gizmos.line(startPos, endPos); // Draw arrow head

            this.drawArrowHead(gizmos, startPos, endPos, 10);
          } // Draw center point


          gizmos.color = Color.WHITE;
          gizmos.strokeWidth = 3;
          const centerSize = 5;
          gizmos.line(new Vec3(worldPos.x - centerSize, worldPos.y, worldPos.z), new Vec3(worldPos.x + centerSize, worldPos.y, worldPos.z));
          gizmos.line(new Vec3(worldPos.x, worldPos.y - centerSize, worldPos.z), new Vec3(worldPos.x, worldPos.y + centerSize, worldPos.z)); // Draw angle indicator text (if supported)

          if (gizmos.text && target.angle > 0) {
            gizmos.color = Color.WHITE;
            const textPos = new Vec3(worldPos.x, worldPos.y + target.radius + 20, worldPos.z);
            gizmos.text(textPos, `Angle: ${target.angle}°\nCount: ${target.count}\nRadius: ${target.radius}`);
          }
        },

        /**
         * Draw an arc using line segments
         */
        drawArc(gizmos, center, radius, startAngle, totalAngle, segments) {
          const angleStep = totalAngle / segments;

          for (let i = 0; i < segments; i++) {
            const angle1 = (startAngle + angleStep * i) * Math.PI / 180;
            const angle2 = (startAngle + angleStep * (i + 1)) * Math.PI / 180;
            const x1 = center.x + Math.cos(angle1) * radius;
            const y1 = center.y + Math.sin(angle1) * radius;
            const x2 = center.x + Math.cos(angle2) * radius;
            const y2 = center.y + Math.sin(angle2) * radius;
            gizmos.line(new Vec3(x1, y1, center.z), new Vec3(x2, y2, center.z));
          }
        },

        /**
         * Draw arrow head at the end of a line
         */
        drawArrowHead(gizmos, start, end, size) {
          // Calculate direction vector
          const dir = new Vec3();
          Vec3.subtract(dir, end, start);
          dir.normalize(); // Calculate perpendicular vector

          const perp = new Vec3(-dir.y, dir.x, 0); // Arrow head points

          const arrowPoint1 = new Vec3();
          const arrowPoint2 = new Vec3();
          Vec3.scaleAndAdd(arrowPoint1, end, dir, -size);
          Vec3.scaleAndAdd(arrowPoint1, arrowPoint1, perp, size * 0.5);
          Vec3.scaleAndAdd(arrowPoint2, end, dir, -size);
          Vec3.scaleAndAdd(arrowPoint2, arrowPoint2, perp, -size * 0.5); // Draw arrow head lines

          gizmos.line(end, arrowPoint1);
          gizmos.line(end, arrowPoint2);
        }

      }; // Export the gizmo

      _export("default", EmitterGizmo);

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=b37c1a13f611ddf8b31757285f1055a41cc2342e.js.map