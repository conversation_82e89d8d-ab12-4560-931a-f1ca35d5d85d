{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/world/bullet/Emitter.ts"], "names": ["_decorator", "Component", "Node", "Vec3", "Color", "ccclass", "property", "executeInEditMode", "Emitter", "type", "isActive", "canTrigger", "setActive", "active", "schedule", "emitBullet", "frequency", "unschedule", "onDrawGizmos", "node", "gizmos", "globalThis", "color", "YELLOW", "strokeWidth", "worldPos", "worldPosition", "radius", "GRAY", "circle", "anglePerBullet", "count", "angle", "startAngle", "drawArc", "RED", "i", "bulletAngle", "angleRad", "Math", "PI", "dirX", "cos", "dirY", "sin", "startPos", "x", "y", "z", "<PERSON><PERSON><PERSON><PERSON>", "max", "endPos", "line", "drawArrowHead", "WHITE", "centerSize", "center", "totalAngle", "segments", "angleStep", "angle1", "angle2", "x1", "y1", "x2", "y2", "start", "end", "size", "dir", "subtract", "normalize", "perp", "arrowPoint1", "arrowPoint2", "scaleAndAdd"], "mappings": ";;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,K,OAAAA,K;;;;;;;;;OACtC;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,QAAX;AAAqBC,QAAAA;AAArB,O,GAA2CP,U;;yBAIpCQ,O,WAFZH,OAAO,CAAC,SAAD,C,UACPE,iBAAiB,CAAC,IAAD,C,UAGbD,QAAQ,CAAC;AAACG,QAAAA,IAAI,EAAEP;AAAP,OAAD,C,0CAJb,MAEaM,OAFb,SAE6BP,SAF7B,CAEuC;AAAA;AAAA;;AAAA;;AAKnC;AALmC;;AASnC;AATmC;;AAanC;AAbmC;;AAiBnC;AAjBmC;;AAqBnC;AArBmC;;AAAA,eAyBnCS,QAzBmC,GAyBf,KAzBe;AAAA;;AA2BnCC,QAAAA,UAAU,GAAY;AAClB;AACA,iBAAO,KAAP;AACH;;AAEDC,QAAAA,SAAS,CAACC,MAAD,EAAwB;AAC7B,eAAKH,QAAL,GAAgBG,MAAhB;;AACA,cAAIA,MAAJ,EAAY;AACR,iBAAKC,QAAL,CAAc,KAAKC,UAAnB,EAA+B,KAAKC,SAApC;AACH,WAFD,MAEO;AACH,iBAAKC,UAAL,CAAgB,KAAKF,UAArB;AACH;AACJ;;AAEDA,QAAAA,UAAU,GAAS,CACf;AAEH;AAED;AACJ;AACA;AACA;;;AACIG,QAAAA,YAAY,GAAS;AACjB,cAAI,CAAC,KAAKC,IAAV,EAAgB;AAEhB,gBAAMC,MAAM,GAAIC,UAAD,CAAoBD,MAAnC;AACA,cAAI,CAACA,MAAL,EAAa,OAJI,CAMjB;;AACAA,UAAAA,MAAM,CAACE,KAAP,GAAelB,KAAK,CAACmB,MAArB;AACAH,UAAAA,MAAM,CAACI,WAAP,GAAqB,CAArB,CARiB,CAUjB;;AACA,gBAAMC,QAAQ,GAAG,KAAKN,IAAL,CAAUO,aAA3B,CAXiB,CAajB;;AACA,cAAI,KAAKC,MAAL,GAAc,CAAlB,EAAqB;AACjBP,YAAAA,MAAM,CAACE,KAAP,GAAelB,KAAK,CAACwB,IAArB;AACAR,YAAAA,MAAM,CAACI,WAAP,GAAqB,CAArB;AACAJ,YAAAA,MAAM,CAACS,MAAP,CAAcJ,QAAd,EAAwB,KAAKE,MAA7B;AACH,WAlBgB,CAoBjB;;;AACA,gBAAMG,cAAc,GAAG,KAAKC,KAAL,GAAa,CAAb,GAAiB,KAAKC,KAAL,IAAc,KAAKD,KAAL,GAAa,CAA3B,CAAjB,GAAiD,CAAxE;AACA,gBAAME,UAAU,GAAG,CAAC,KAAKD,KAAN,GAAc,CAAjC,CAtBiB,CAsBmB;AAEpC;;AACA,cAAI,KAAKA,KAAL,GAAa,CAAb,IAAkB,KAAKL,MAAL,GAAc,CAApC,EAAuC;AACnCP,YAAAA,MAAM,CAACE,KAAP,GAAelB,KAAK,CAACmB,MAArB;AACAH,YAAAA,MAAM,CAACI,WAAP,GAAqB,CAArB;AACA,iBAAKU,OAAL,CAAad,MAAb,EAAqBK,QAArB,EAA+B,KAAKE,MAApC,EAA4CM,UAA5C,EAAwD,KAAKD,KAA7D,EAAoE,EAApE;AACH,WA7BgB,CA+BjB;;;AACAZ,UAAAA,MAAM,CAACE,KAAP,GAAelB,KAAK,CAAC+B,GAArB;AACAf,UAAAA,MAAM,CAACI,WAAP,GAAqB,CAArB;;AAEA,eAAK,IAAIY,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKL,KAAzB,EAAgCK,CAAC,EAAjC,EAAqC;AACjC,gBAAIC,WAAJ;;AAEA,gBAAI,KAAKN,KAAL,KAAe,CAAnB,EAAsB;AAClBM,cAAAA,WAAW,GAAG,CAAd,CADkB,CACD;AACpB,aAFD,MAEO;AACHA,cAAAA,WAAW,GAAGJ,UAAU,GAAIH,cAAc,GAAGM,CAA7C;AACH,aAPgC,CASjC;;;AACA,kBAAME,QAAQ,GAAID,WAAW,GAAGE,IAAI,CAACC,EAApB,GAA0B,GAA3C,CAViC,CAYjC;;AACA,kBAAMC,IAAI,GAAGF,IAAI,CAACG,GAAL,CAASJ,QAAT,CAAb;AACA,kBAAMK,IAAI,GAAGJ,IAAI,CAACK,GAAL,CAASN,QAAT,CAAb,CAdiC,CAgBjC;;AACA,kBAAMO,QAAQ,GAAG,IAAI1C,IAAJ,CACbsB,QAAQ,CAACqB,CAAT,GAAaL,IAAI,GAAG,KAAKd,MADZ,EAEbF,QAAQ,CAACsB,CAAT,GAAaJ,IAAI,GAAG,KAAKhB,MAFZ,EAGbF,QAAQ,CAACuB,CAHI,CAAjB,CAjBiC,CAuBjC;;AACA,kBAAMC,WAAW,GAAGV,IAAI,CAACW,GAAL,CAAS,EAAT,EAAa,KAAKvB,MAAL,GAAc,GAA3B,CAApB;AACA,kBAAMwB,MAAM,GAAG,IAAIhD,IAAJ,CACX0C,QAAQ,CAACC,CAAT,GAAaL,IAAI,GAAGQ,WADT,EAEXJ,QAAQ,CAACE,CAAT,GAAaJ,IAAI,GAAGM,WAFT,EAGXJ,QAAQ,CAACG,CAHE,CAAf,CAzBiC,CA+BjC;;AACA5B,YAAAA,MAAM,CAACgC,IAAP,CAAYP,QAAZ,EAAsBM,MAAtB,EAhCiC,CAkCjC;;AACA,iBAAKE,aAAL,CAAmBjC,MAAnB,EAA2ByB,QAA3B,EAAqCM,MAArC,EAA6C,EAA7C;AACH,WAvEgB,CAyEjB;;;AACA/B,UAAAA,MAAM,CAACE,KAAP,GAAelB,KAAK,CAACkD,KAArB;AACAlC,UAAAA,MAAM,CAACI,WAAP,GAAqB,CAArB;AACA,gBAAM+B,UAAU,GAAG,CAAnB;AACAnC,UAAAA,MAAM,CAACgC,IAAP,CACI,IAAIjD,IAAJ,CAASsB,QAAQ,CAACqB,CAAT,GAAaS,UAAtB,EAAkC9B,QAAQ,CAACsB,CAA3C,EAA8CtB,QAAQ,CAACuB,CAAvD,CADJ,EAEI,IAAI7C,IAAJ,CAASsB,QAAQ,CAACqB,CAAT,GAAaS,UAAtB,EAAkC9B,QAAQ,CAACsB,CAA3C,EAA8CtB,QAAQ,CAACuB,CAAvD,CAFJ;AAIA5B,UAAAA,MAAM,CAACgC,IAAP,CACI,IAAIjD,IAAJ,CAASsB,QAAQ,CAACqB,CAAlB,EAAqBrB,QAAQ,CAACsB,CAAT,GAAaQ,UAAlC,EAA8C9B,QAAQ,CAACuB,CAAvD,CADJ,EAEI,IAAI7C,IAAJ,CAASsB,QAAQ,CAACqB,CAAlB,EAAqBrB,QAAQ,CAACsB,CAAT,GAAaQ,UAAlC,EAA8C9B,QAAQ,CAACuB,CAAvD,CAFJ;AAIH;AAED;AACJ;AACA;;;AACYd,QAAAA,OAAO,CAACd,MAAD,EAAcoC,MAAd,EAA4B7B,MAA5B,EAA4CM,UAA5C,EAAgEwB,UAAhE,EAAoFC,QAApF,EAA4G;AACvH,gBAAMC,SAAS,GAAGF,UAAU,GAAGC,QAA/B;;AAEA,eAAK,IAAItB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGsB,QAApB,EAA8BtB,CAAC,EAA/B,EAAmC;AAC/B,kBAAMwB,MAAM,GAAG,CAAC3B,UAAU,GAAG0B,SAAS,GAAGvB,CAA1B,IAA+BG,IAAI,CAACC,EAApC,GAAyC,GAAxD;AACA,kBAAMqB,MAAM,GAAG,CAAC5B,UAAU,GAAG0B,SAAS,IAAIvB,CAAC,GAAG,CAAR,CAAvB,IAAqCG,IAAI,CAACC,EAA1C,GAA+C,GAA9D;AAEA,kBAAMsB,EAAE,GAAGN,MAAM,CAACV,CAAP,GAAWP,IAAI,CAACG,GAAL,CAASkB,MAAT,IAAmBjC,MAAzC;AACA,kBAAMoC,EAAE,GAAGP,MAAM,CAACT,CAAP,GAAWR,IAAI,CAACK,GAAL,CAASgB,MAAT,IAAmBjC,MAAzC;AACA,kBAAMqC,EAAE,GAAGR,MAAM,CAACV,CAAP,GAAWP,IAAI,CAACG,GAAL,CAASmB,MAAT,IAAmBlC,MAAzC;AACA,kBAAMsC,EAAE,GAAGT,MAAM,CAACT,CAAP,GAAWR,IAAI,CAACK,GAAL,CAASiB,MAAT,IAAmBlC,MAAzC;AAEAP,YAAAA,MAAM,CAACgC,IAAP,CACI,IAAIjD,IAAJ,CAAS2D,EAAT,EAAaC,EAAb,EAAiBP,MAAM,CAACR,CAAxB,CADJ,EAEI,IAAI7C,IAAJ,CAAS6D,EAAT,EAAaC,EAAb,EAAiBT,MAAM,CAACR,CAAxB,CAFJ;AAIH;AACJ;AAED;AACJ;AACA;;;AACYK,QAAAA,aAAa,CAACjC,MAAD,EAAc8C,KAAd,EAA2BC,GAA3B,EAAsCC,IAAtC,EAA0D;AAC3E;AACA,gBAAMC,GAAG,GAAG,IAAIlE,IAAJ,EAAZ;AACAA,UAAAA,IAAI,CAACmE,QAAL,CAAcD,GAAd,EAAmBF,GAAnB,EAAwBD,KAAxB;AACAG,UAAAA,GAAG,CAACE,SAAJ,GAJ2E,CAM3E;;AACA,gBAAMC,IAAI,GAAG,IAAIrE,IAAJ,CAAS,CAACkE,GAAG,CAACtB,CAAd,EAAiBsB,GAAG,CAACvB,CAArB,EAAwB,CAAxB,CAAb,CAP2E,CAS3E;;AACA,gBAAM2B,WAAW,GAAG,IAAItE,IAAJ,EAApB;AACA,gBAAMuE,WAAW,GAAG,IAAIvE,IAAJ,EAApB;AAEAA,UAAAA,IAAI,CAACwE,WAAL,CAAiBF,WAAjB,EAA8BN,GAA9B,EAAmCE,GAAnC,EAAwC,CAACD,IAAzC;AACAjE,UAAAA,IAAI,CAACwE,WAAL,CAAiBF,WAAjB,EAA8BA,WAA9B,EAA2CD,IAA3C,EAAiDJ,IAAI,GAAG,GAAxD;AAEAjE,UAAAA,IAAI,CAACwE,WAAL,CAAiBD,WAAjB,EAA8BP,GAA9B,EAAmCE,GAAnC,EAAwC,CAACD,IAAzC;AACAjE,UAAAA,IAAI,CAACwE,WAAL,CAAiBD,WAAjB,EAA8BA,WAA9B,EAA2CF,IAA3C,EAAiD,CAACJ,IAAD,GAAQ,GAAzD,EAjB2E,CAmB3E;;AACAhD,UAAAA,MAAM,CAACgC,IAAP,CAAYe,GAAZ,EAAiBM,WAAjB;AACArD,UAAAA,MAAM,CAACgC,IAAP,CAAYe,GAAZ,EAAiBO,WAAjB;AACH;;AAxLkC,O;;;;;iBAGd,I;;gFAGpBpE,Q;;;;;iBACe,E;;iFAGfA,Q;;;;;iBACgB,C;;gFAGhBA,Q;;;;;iBACe,C;;gFAGfA,Q;;;;;iBACe,C;;oFAGfA,Q;;;;;iBACmB,C", "sourcesContent": ["import { _decorator, Component, Node, Vec3, Color, gfx } from 'cc';\r\nconst { ccclass, property, executeInEditMode } = _decorator;\r\n\r\n@ccclass('Emitter')\r\n@executeInEditMode(true)\r\nexport class Emitter extends Component {\r\n\r\n    @property({type: Node})\r\n    bulletPrefab: Node = null;\r\n\r\n    // 发射角度\r\n    @property\r\n    angle: number = 90;\r\n\r\n    // 发射半径\r\n    @property\r\n    radius: number = 0;\r\n\r\n    // 发射条数\r\n    @property\r\n    count: number = 1;\r\n\r\n    // 子弹初速度\r\n    @property\r\n    speed: number = 1;\r\n\r\n    // 频率(间隔多少秒发射一次)\r\n    @property\r\n    frequency: number = 1;\r\n\r\n    isActive: boolean = false;\r\n\r\n    canTrigger(): boolean {\r\n        // 检查是否可以触发发射\r\n        return false;\r\n    }\r\n\r\n    setActive(active: boolean): void {\r\n        this.isActive = active;\r\n        if (active) {\r\n            this.schedule(this.emitBullet, this.frequency);\r\n        } else {\r\n            this.unschedule(this.emitBullet);\r\n        }\r\n    }\r\n\r\n    emitBullet(): void {\r\n        // 发射子弹\r\n\r\n    }\r\n\r\n    /**\r\n     * Draw gizmos for the emitter in the editor\r\n     * Shows the emission arc, radius, and bullet directions\r\n     */\r\n    onDrawGizmos(): void {\r\n        if (!this.node) return;\r\n\r\n        const gizmos = (globalThis as any).gizmos;\r\n        if (!gizmos) return;\r\n\r\n        // Set gizmo color\r\n        gizmos.color = Color.YELLOW;\r\n        gizmos.strokeWidth = 2;\r\n\r\n        // Get world position\r\n        const worldPos = this.node.worldPosition;\r\n\r\n        // Draw emission radius circle (optional, for reference)\r\n        if (this.radius > 0) {\r\n            gizmos.color = Color.GRAY;\r\n            gizmos.strokeWidth = 1;\r\n            gizmos.circle(worldPos, this.radius);\r\n        }\r\n\r\n        // Calculate angle per bullet\r\n        const anglePerBullet = this.count > 1 ? this.angle / (this.count - 1) : 0;\r\n        const startAngle = -this.angle / 2; // Start from negative half angle\r\n\r\n        // Draw emission arc\r\n        if (this.angle > 0 && this.radius > 0) {\r\n            gizmos.color = Color.YELLOW;\r\n            gizmos.strokeWidth = 2;\r\n            this.drawArc(gizmos, worldPos, this.radius, startAngle, this.angle, 32);\r\n        }\r\n\r\n        // Draw bullet direction arrows\r\n        gizmos.color = Color.RED;\r\n        gizmos.strokeWidth = 2;\r\n\r\n        for (let i = 0; i < this.count; i++) {\r\n            let bulletAngle: number;\r\n\r\n            if (this.count === 1) {\r\n                bulletAngle = 0; // Single bullet goes straight\r\n            } else {\r\n                bulletAngle = startAngle + (anglePerBullet * i);\r\n            }\r\n\r\n            // Convert angle to radians\r\n            const angleRad = (bulletAngle * Math.PI) / 180;\r\n\r\n            // Calculate direction vector\r\n            const dirX = Math.cos(angleRad);\r\n            const dirY = Math.sin(angleRad);\r\n\r\n            // Start position (at radius distance from center)\r\n            const startPos = new Vec3(\r\n                worldPos.x + dirX * this.radius,\r\n                worldPos.y + dirY * this.radius,\r\n                worldPos.z\r\n            );\r\n\r\n            // End position (arrow length)\r\n            const arrowLength = Math.max(50, this.radius * 0.5);\r\n            const endPos = new Vec3(\r\n                startPos.x + dirX * arrowLength,\r\n                startPos.y + dirY * arrowLength,\r\n                startPos.z\r\n            );\r\n\r\n            // Draw arrow line\r\n            gizmos.line(startPos, endPos);\r\n\r\n            // Draw arrow head\r\n            this.drawArrowHead(gizmos, startPos, endPos, 10);\r\n        }\r\n\r\n        // Draw center point\r\n        gizmos.color = Color.WHITE;\r\n        gizmos.strokeWidth = 3;\r\n        const centerSize = 5;\r\n        gizmos.line(\r\n            new Vec3(worldPos.x - centerSize, worldPos.y, worldPos.z),\r\n            new Vec3(worldPos.x + centerSize, worldPos.y, worldPos.z)\r\n        );\r\n        gizmos.line(\r\n            new Vec3(worldPos.x, worldPos.y - centerSize, worldPos.z),\r\n            new Vec3(worldPos.x, worldPos.y + centerSize, worldPos.z)\r\n        );\r\n    }\r\n\r\n    /**\r\n     * Draw an arc using line segments\r\n     */\r\n    private drawArc(gizmos: any, center: Vec3, radius: number, startAngle: number, totalAngle: number, segments: number): void {\r\n        const angleStep = totalAngle / segments;\r\n\r\n        for (let i = 0; i < segments; i++) {\r\n            const angle1 = (startAngle + angleStep * i) * Math.PI / 180;\r\n            const angle2 = (startAngle + angleStep * (i + 1)) * Math.PI / 180;\r\n\r\n            const x1 = center.x + Math.cos(angle1) * radius;\r\n            const y1 = center.y + Math.sin(angle1) * radius;\r\n            const x2 = center.x + Math.cos(angle2) * radius;\r\n            const y2 = center.y + Math.sin(angle2) * radius;\r\n\r\n            gizmos.line(\r\n                new Vec3(x1, y1, center.z),\r\n                new Vec3(x2, y2, center.z)\r\n            );\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Draw arrow head at the end of a line\r\n     */\r\n    private drawArrowHead(gizmos: any, start: Vec3, end: Vec3, size: number): void {\r\n        // Calculate direction vector\r\n        const dir = new Vec3();\r\n        Vec3.subtract(dir, end, start);\r\n        dir.normalize();\r\n\r\n        // Calculate perpendicular vector\r\n        const perp = new Vec3(-dir.y, dir.x, 0);\r\n\r\n        // Arrow head points\r\n        const arrowPoint1 = new Vec3();\r\n        const arrowPoint2 = new Vec3();\r\n\r\n        Vec3.scaleAndAdd(arrowPoint1, end, dir, -size);\r\n        Vec3.scaleAndAdd(arrowPoint1, arrowPoint1, perp, size * 0.5);\r\n\r\n        Vec3.scaleAndAdd(arrowPoint2, end, dir, -size);\r\n        Vec3.scaleAndAdd(arrowPoint2, arrowPoint2, perp, -size * 0.5);\r\n\r\n        // Draw arrow head lines\r\n        gizmos.line(end, arrowPoint1);\r\n        gizmos.line(end, arrowPoint2);\r\n    }\r\n}\r\n\r\n\r\n"]}