System.register(["cc"], function (_export, _context) {
  "use strict";

  var _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, Node, _dec, _dec2, _class, _class2, _descriptor, _descriptor2, _descriptor3, _descriptor4, _descriptor5, _descriptor6, _descriptor7, _crd, ccclass, property, Emitter;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      Node = _cc.Node;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "2564dArcRFKZKoo3odCQrHw", "Emitter", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Node']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("Emitter", Emitter = (_dec = ccclass('Emitter'), _dec2 = property({
        type: Node
      }), _dec(_class = (_class2 = class Emitter extends Component {
        constructor(...args) {
          super(...args);

          _initializerDefineProperty(this, "bulletPrefab", _descriptor, this);

          // 发射角度
          _initializerDefineProperty(this, "angle", _descriptor2, this);

          // 发射弧度
          _initializerDefineProperty(this, "arc", _descriptor3, this);

          // 发射半径
          _initializerDefineProperty(this, "radius", _descriptor4, this);

          // 发射条数
          _initializerDefineProperty(this, "count", _descriptor5, this);

          // 子弹初速度
          _initializerDefineProperty(this, "speed", _descriptor6, this);

          // 频率(间隔多少秒发射一次)
          _initializerDefineProperty(this, "frequency", _descriptor7, this);

          this.isActive = false;
        }

        canTrigger() {
          // 检查是否可以触发发射
          return false;
        }

        setActive(active) {
          this.isActive = active;

          if (active) {
            this.schedule(this.emitBullet, this.frequency);
          } else {
            this.unschedule(this.emitBullet);
          }
        }

        emitBullet() {// 发射子弹
        }

        getDirection(index) {
          // 计算发射方向
          const angleOffset = this.arc / (this.count - 1) * index - this.arc / 2;
          const radian = (this.angle + angleOffset) * (Math.PI / 180);
          return {
            x: Math.cos(radian),
            y: Math.sin(radian)
          };
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "bulletPrefab", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "angle", [property], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 90;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "arc", [property], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 0;
        }
      }), _descriptor4 = _applyDecoratedDescriptor(_class2.prototype, "radius", [property], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 0;
        }
      }), _descriptor5 = _applyDecoratedDescriptor(_class2.prototype, "count", [property], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 1;
        }
      }), _descriptor6 = _applyDecoratedDescriptor(_class2.prototype, "speed", [property], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 1;
        }
      }), _descriptor7 = _applyDecoratedDescriptor(_class2.prototype, "frequency", [property], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 1;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=eb065a22717f447f6d2a5623f6f7fdb1b64865a1.js.map