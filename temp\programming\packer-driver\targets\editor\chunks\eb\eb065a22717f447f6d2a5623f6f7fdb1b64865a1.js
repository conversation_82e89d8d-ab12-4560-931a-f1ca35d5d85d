System.register(["cc"], function (_export, _context) {
  "use strict";

  var _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, Node, Vec3, Color, _dec, _dec2, _dec3, _class, _class2, _descriptor, _descriptor2, _descriptor3, _descriptor4, _descriptor5, _descriptor6, _crd, ccclass, property, executeInEditMode, Emitter;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      Node = _cc.Node;
      Vec3 = _cc.Vec3;
      Color = _cc.Color;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "2564dArcRFKZKoo3odCQrHw", "Emitter", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Node', 'Vec3', 'Color', 'gfx']);

      ({
        ccclass,
        property,
        executeInEditMode
      } = _decorator);

      _export("Emitter", Emitter = (_dec = ccclass('Emitter'), _dec2 = executeInEditMode(true), _dec3 = property({
        type: Node
      }), _dec(_class = _dec2(_class = (_class2 = class Emitter extends Component {
        constructor(...args) {
          super(...args);

          _initializerDefineProperty(this, "bulletPrefab", _descriptor, this);

          // 发射角度
          _initializerDefineProperty(this, "angle", _descriptor2, this);

          // 发射半径
          _initializerDefineProperty(this, "radius", _descriptor3, this);

          // 发射条数
          _initializerDefineProperty(this, "count", _descriptor4, this);

          // 子弹初速度
          _initializerDefineProperty(this, "speed", _descriptor5, this);

          // 频率(间隔多少秒发射一次)
          _initializerDefineProperty(this, "frequency", _descriptor6, this);

          this.isActive = false;
        }

        canTrigger() {
          // 检查是否可以触发发射
          return false;
        }

        setActive(active) {
          this.isActive = active;

          if (active) {
            this.schedule(this.emitBullet, this.frequency);
          } else {
            this.unschedule(this.emitBullet);
          }
        }

        emitBullet() {// 发射子弹
        }
        /**
         * Draw gizmos for the emitter in the editor
         * Shows the emission arc, radius, and bullet directions
         */


        onDrawGizmos() {
          if (!this.node) return;
          const gizmos = globalThis.gizmos;
          if (!gizmos) return; // Set gizmo color

          gizmos.color = Color.YELLOW;
          gizmos.strokeWidth = 2; // Get world position

          const worldPos = this.node.worldPosition; // Draw emission radius circle (optional, for reference)

          if (this.radius > 0) {
            gizmos.color = Color.GRAY;
            gizmos.strokeWidth = 1;
            gizmos.circle(worldPos, this.radius);
          } // Calculate angle per bullet


          const anglePerBullet = this.count > 1 ? this.angle / (this.count - 1) : 0;
          const startAngle = -this.angle / 2; // Start from negative half angle
          // Draw emission arc

          if (this.angle > 0 && this.radius > 0) {
            gizmos.color = Color.YELLOW;
            gizmos.strokeWidth = 2;
            this.drawArc(gizmos, worldPos, this.radius, startAngle, this.angle, 32);
          } // Draw bullet direction arrows


          gizmos.color = Color.RED;
          gizmos.strokeWidth = 2;

          for (let i = 0; i < this.count; i++) {
            let bulletAngle;

            if (this.count === 1) {
              bulletAngle = 0; // Single bullet goes straight
            } else {
              bulletAngle = startAngle + anglePerBullet * i;
            } // Convert angle to radians


            const angleRad = bulletAngle * Math.PI / 180; // Calculate direction vector

            const dirX = Math.cos(angleRad);
            const dirY = Math.sin(angleRad); // Start position (at radius distance from center)

            const startPos = new Vec3(worldPos.x + dirX * this.radius, worldPos.y + dirY * this.radius, worldPos.z); // End position (arrow length)

            const arrowLength = Math.max(50, this.radius * 0.5);
            const endPos = new Vec3(startPos.x + dirX * arrowLength, startPos.y + dirY * arrowLength, startPos.z); // Draw arrow line

            gizmos.line(startPos, endPos); // Draw arrow head

            this.drawArrowHead(gizmos, startPos, endPos, 10);
          } // Draw center point


          gizmos.color = Color.WHITE;
          gizmos.strokeWidth = 3;
          const centerSize = 5;
          gizmos.line(new Vec3(worldPos.x - centerSize, worldPos.y, worldPos.z), new Vec3(worldPos.x + centerSize, worldPos.y, worldPos.z));
          gizmos.line(new Vec3(worldPos.x, worldPos.y - centerSize, worldPos.z), new Vec3(worldPos.x, worldPos.y + centerSize, worldPos.z));
        }
        /**
         * Draw an arc using line segments
         */


        drawArc(gizmos, center, radius, startAngle, totalAngle, segments) {
          const angleStep = totalAngle / segments;

          for (let i = 0; i < segments; i++) {
            const angle1 = (startAngle + angleStep * i) * Math.PI / 180;
            const angle2 = (startAngle + angleStep * (i + 1)) * Math.PI / 180;
            const x1 = center.x + Math.cos(angle1) * radius;
            const y1 = center.y + Math.sin(angle1) * radius;
            const x2 = center.x + Math.cos(angle2) * radius;
            const y2 = center.y + Math.sin(angle2) * radius;
            gizmos.line(new Vec3(x1, y1, center.z), new Vec3(x2, y2, center.z));
          }
        }
        /**
         * Draw arrow head at the end of a line
         */


        drawArrowHead(gizmos, start, end, size) {
          // Calculate direction vector
          const dir = new Vec3();
          Vec3.subtract(dir, end, start);
          dir.normalize(); // Calculate perpendicular vector

          const perp = new Vec3(-dir.y, dir.x, 0); // Arrow head points

          const arrowPoint1 = new Vec3();
          const arrowPoint2 = new Vec3();
          Vec3.scaleAndAdd(arrowPoint1, end, dir, -size);
          Vec3.scaleAndAdd(arrowPoint1, arrowPoint1, perp, size * 0.5);
          Vec3.scaleAndAdd(arrowPoint2, end, dir, -size);
          Vec3.scaleAndAdd(arrowPoint2, arrowPoint2, perp, -size * 0.5); // Draw arrow head lines

          gizmos.line(end, arrowPoint1);
          gizmos.line(end, arrowPoint2);
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "bulletPrefab", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "angle", [property], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 90;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "radius", [property], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 0;
        }
      }), _descriptor4 = _applyDecoratedDescriptor(_class2.prototype, "count", [property], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 1;
        }
      }), _descriptor5 = _applyDecoratedDescriptor(_class2.prototype, "speed", [property], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 1;
        }
      }), _descriptor6 = _applyDecoratedDescriptor(_class2.prototype, "frequency", [property], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 1;
        }
      })), _class2)) || _class) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=eb065a22717f447f6d2a5623f6f7fdb1b64865a1.js.map