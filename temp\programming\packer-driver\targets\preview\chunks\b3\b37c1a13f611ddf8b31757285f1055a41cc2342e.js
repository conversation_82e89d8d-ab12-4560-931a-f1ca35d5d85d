System.register(["cc", "cc/env"], function (_export, _context) {
  "use strict";

  var _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, Color, Graphics, EDITOR, _dec, _dec2, _class, _class2, _descriptor, _descriptor2, _descriptor3, _descriptor4, _descriptor5, _descriptor6, _descriptor7, _descriptor8, _descriptor9, _crd, ccclass, property, executeInEditMode, EmitterGizmo;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      Color = _cc.Color;
      Graphics = _cc.Graphics;
    }, function (_ccEnv) {
      EDITOR = _ccEnv.EDITOR;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "92b82K+6DhHdIKGhR3HTOWv", "EmitterGizmo", undefined);
      /**
       * Emitter Gizmo Component
       * This component provides visual debugging for Emitter components in the scene view
       * It should be added to the same node as the Emitter component
       */


      __checkObsolete__(['_decorator', 'Component', 'Color', 'Graphics']);

      ({
        ccclass,
        property,
        executeInEditMode
      } = _decorator);

      _export("EmitterGizmo", EmitterGizmo = (_dec = ccclass('EmitterGizmo'), _dec2 = executeInEditMode(true), _dec(_class = _dec2(_class = (_class2 = class EmitterGizmo extends Component {
        constructor() {
          super(...arguments);

          _initializerDefineProperty(this, "showRadius", _descriptor, this);

          _initializerDefineProperty(this, "showDirections", _descriptor2, this);

          _initializerDefineProperty(this, "showCenter", _descriptor3, this);

          _initializerDefineProperty(this, "showArc", _descriptor4, this);

          _initializerDefineProperty(this, "radiusColor", _descriptor5, this);

          _initializerDefineProperty(this, "directionColor", _descriptor6, this);

          _initializerDefineProperty(this, "centerColor", _descriptor7, this);

          _initializerDefineProperty(this, "arcColor", _descriptor8, this);

          _initializerDefineProperty(this, "speedScale", _descriptor9, this);

          this.graphics = null;
          this.emitter = null;
        }

        onLoad() {
          if (!EDITOR) return; // Get or create Graphics component

          this.graphics = this.getComponent(Graphics) || this.addComponent(Graphics); // Get Emitter component

          this.emitter = this.getComponent('Emitter');

          if (!this.emitter) {
            console.warn('EmitterGizmo: No Emitter component found on this node');
          }
        }

        update() {
          if (!EDITOR || !this.graphics || !this.emitter) return;
          this.drawGizmos();
        }

        drawGizmos() {
          if (!this.graphics) return; // Clear previous drawings

          this.graphics.clear(); // Draw center point

          if (this.showCenter) {
            this.drawCenter();
          } // Draw radius circle


          if (this.showRadius && this.emitter.radius > 0) {
            this.drawRadius();
          } // Draw arc visualization


          if (this.showArc && this.emitter.arc > 0) {
            this.drawArc();
          } // Draw direction arrows


          if (this.showDirections) {
            this.drawDirections();
          }
        }

        drawCenter() {
          if (!this.graphics) return;
          this.graphics.strokeColor = this.centerColor;
          this.graphics.lineWidth = 2;
          var centerSize = 8; // Draw cross at center

          this.graphics.moveTo(-centerSize, 0);
          this.graphics.lineTo(centerSize, 0);
          this.graphics.moveTo(0, -centerSize);
          this.graphics.lineTo(0, centerSize);
          this.graphics.stroke();
        }

        drawRadius() {
          if (!this.graphics) return;
          this.graphics.strokeColor = this.radiusColor;
          this.graphics.lineWidth = 1; // Draw radius circle

          this.graphics.circle(0, 0, this.emitter.radius);
          this.graphics.stroke();
        }

        drawArc() {
          if (!this.graphics) return;
          this.graphics.strokeColor = this.arcColor;
          this.graphics.lineWidth = 2;
          var baseDirection = this.emitter.angle || 0;
          var totalArc = this.emitter.arc || 0;
          var startRadius = this.emitter.radius || 0; // Start from emitter radius

          var speedFactor = this.emitter.speed || 1;
          var endRadius = startRadius + speedFactor * this.speedScale * 30; // End radius based on speed
          // Calculate start and end angles for the arc

          var startAngle = (baseDirection - totalArc / 2 + 90) * Math.PI / 180; // +90 for Cocos coordinate system

          var endAngle = (baseDirection + totalArc / 2 + 90) * Math.PI / 180; // Draw arc at start radius (emitter radius)

          var segments = Math.max(8, Math.floor(totalArc / 5)); // More segments for larger arcs

          var angleStep = (endAngle - startAngle) / segments;

          for (var i = 0; i < segments; i++) {
            var angle1 = startAngle + angleStep * i;
            var angle2 = startAngle + angleStep * (i + 1);
            var x1 = Math.cos(angle1) * startRadius;
            var y1 = Math.sin(angle1) * startRadius;
            var x2 = Math.cos(angle2) * startRadius;
            var y2 = Math.sin(angle2) * startRadius;
            this.graphics.moveTo(x1, y1);
            this.graphics.lineTo(x2, y2);
          } // Draw arc at end radius (speed-based)


          for (var _i = 0; _i < segments; _i++) {
            var _angle = startAngle + angleStep * _i;

            var _angle2 = startAngle + angleStep * (_i + 1);

            var _x = Math.cos(_angle) * endRadius;

            var _y = Math.sin(_angle) * endRadius;

            var _x2 = Math.cos(_angle2) * endRadius;

            var _y2 = Math.sin(_angle2) * endRadius;

            this.graphics.moveTo(_x, _y);
            this.graphics.lineTo(_x2, _y2);
          } // Draw connecting lines between start and end arcs


          var connectionLines = 5; // Number of radial lines connecting the arcs

          for (var _i2 = 0; _i2 <= connectionLines; _i2++) {
            var t = _i2 / connectionLines;
            var angle = startAngle + (endAngle - startAngle) * t;
            var startX = Math.cos(angle) * startRadius;
            var startY = Math.sin(angle) * startRadius;
            var endX = Math.cos(angle) * endRadius;
            var endY = Math.sin(angle) * endRadius;
            this.graphics.moveTo(startX, startY);
            this.graphics.lineTo(endX, endY);
          }

          this.graphics.stroke();
        }

        drawDirections() {
          if (!this.graphics || this.emitter.count <= 0) return;
          this.graphics.strokeColor = this.directionColor;
          this.graphics.lineWidth = 2; // Use arc property for spread calculation, angle for direction

          var baseDirection = this.emitter.angle || 0; // Base direction from angle property

          var totalArc = this.emitter.arc || 0; // Total arc to spread bullets across
          // Calculate angle per bullet based on arc and count

          var anglePerBullet = this.emitter.count > 1 ? totalArc / (this.emitter.count - 1) : 0;
          var startAngle = baseDirection - totalArc / 2; // Start from base direction minus half arc

          for (var i = 0; i < this.emitter.count; i++) {
            var bulletAngle = void 0;

            if (this.emitter.count === 1) {
              bulletAngle = baseDirection; // Single bullet goes in base direction
            } else {
              bulletAngle = startAngle + anglePerBullet * i;
            } // Convert angle to radians (0 degrees = up in Cocos Creator)


            var angleRad = (bulletAngle + 90) * Math.PI / 180; // Calculate direction vector

            var dirX = Math.cos(angleRad);
            var dirY = Math.sin(angleRad); // Start position (at radius distance from center)

            var startX = dirX * this.emitter.radius;
            var startY = dirY * this.emitter.radius; // Calculate arrow length based on speed factor
            // Base length of 30 pixels, scaled by speed factor and speedScale property

            var baseLength = 30;
            var speedFactor = this.emitter.speed || 1; // Default to 1 if speed is 0 or undefined

            var arrowLength = Math.max(baseLength, baseLength * speedFactor * this.speedScale);
            var endX = startX + dirX * arrowLength;
            var endY = startY + dirY * arrowLength; // Draw arrow line

            this.graphics.moveTo(startX, startY);
            this.graphics.lineTo(endX, endY); // Draw arrow head

            this.drawArrowHead(endX, endY, dirX, dirY);
          }

          this.graphics.stroke();
        }

        drawArrowHead(endX, endY, dirX, dirY) {
          if (!this.graphics) return;
          var arrowSize = 8; // Calculate arrow head points

          var arrowAngle = Math.PI / 6; // 30 degrees
          // Left arrow point

          var leftX = endX - arrowSize * (dirX * Math.cos(arrowAngle) - dirY * Math.sin(arrowAngle));
          var leftY = endY - arrowSize * (dirY * Math.cos(arrowAngle) + dirX * Math.sin(arrowAngle)); // Right arrow point

          var rightX = endX - arrowSize * (dirX * Math.cos(-arrowAngle) - dirY * Math.sin(-arrowAngle));
          var rightY = endY - arrowSize * (dirY * Math.cos(-arrowAngle) + dirX * Math.sin(-arrowAngle)); // Draw arrow head lines

          this.graphics.moveTo(endX, endY);
          this.graphics.lineTo(leftX, leftY);
          this.graphics.moveTo(endX, endY);
          this.graphics.lineTo(rightX, rightY);
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "showRadius", [property], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return true;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "showDirections", [property], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return true;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "showCenter", [property], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return true;
        }
      }), _descriptor4 = _applyDecoratedDescriptor(_class2.prototype, "showArc", [property], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return true;
        }
      }), _descriptor5 = _applyDecoratedDescriptor(_class2.prototype, "radiusColor", [property], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return Color.GRAY;
        }
      }), _descriptor6 = _applyDecoratedDescriptor(_class2.prototype, "directionColor", [property], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return Color.RED;
        }
      }), _descriptor7 = _applyDecoratedDescriptor(_class2.prototype, "centerColor", [property], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return Color.WHITE;
        }
      }), _descriptor8 = _applyDecoratedDescriptor(_class2.prototype, "arcColor", [property], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return Color.YELLOW;
        }
      }), _descriptor9 = _applyDecoratedDescriptor(_class2.prototype, "speedScale", [property], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 1.0;
        }
      })), _class2)) || _class) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=b37c1a13f611ddf8b31757285f1055a41cc2342e.js.map