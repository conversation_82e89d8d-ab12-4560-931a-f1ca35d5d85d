{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/gizmos/EmitterGizmo.ts"], "names": ["_decorator", "Component", "Color", "Graphics", "EDITOR", "ccclass", "property", "executeInEditMode", "EmitterGizmo", "graphics", "emitter", "onLoad", "getComponent", "addComponent", "console", "warn", "update", "drawGizmos", "clear", "showCenter", "drawCenter", "showRadius", "radius", "drawRadius", "showArc", "arc", "drawArc", "showDirections", "drawDirections", "strokeColor", "centerColor", "lineWidth", "centerSize", "moveTo", "lineTo", "stroke", "radiusColor", "circle", "arcColor", "baseDirection", "angle", "totalArc", "arcRadius", "Math", "max", "startAngle", "PI", "endAngle", "segments", "floor", "angleStep", "i", "angle1", "angle2", "x1", "cos", "y1", "sin", "x2", "y2", "markerSize", "startX", "startY", "endX", "endY", "count", "directionColor", "anglePerBullet", "bulletAngle", "angleRad", "dirX", "dirY", "baseLength", "speedFactor", "speed", "<PERSON><PERSON><PERSON><PERSON>", "speedScale", "drawArrowHead", "arrowSize", "arrowAngle", "leftX", "leftY", "rightX", "rightY", "GRAY", "RED", "WHITE", "YELLOW"], "mappings": ";;;;;;;;;;;;;;;;AAMSA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,Q,OAAAA,Q;;AAC9BC,MAAAA,M,UAAAA,M;;;;;;AAPT;AACA;AACA;AACA;AACA;;;;;OAIM;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,QAAX;AAAqBC,QAAAA;AAArB,O,GAA2CP,U;;8BAIpCQ,Y,WAFZH,OAAO,CAAC,cAAD,C,UACPE,iBAAiB,CAAC,IAAD,C,0CADlB,MAEaC,YAFb,SAEkCP,SAFlC,CAE4C;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,eA6BhCQ,QA7BgC,GA6BJ,IA7BI;AAAA,eA8BhCC,OA9BgC,GA8BjB,IA9BiB;AAAA;;AAgC9BC,QAAAA,MAAM,GAAS;AACrB,cAAI,CAACP,MAAL,EAAa,OADQ,CAGrB;;AACA,eAAKK,QAAL,GAAgB,KAAKG,YAAL,CAAkBT,QAAlB,KAA+B,KAAKU,YAAL,CAAkBV,QAAlB,CAA/C,CAJqB,CAMrB;;AACA,eAAKO,OAAL,GAAe,KAAKE,YAAL,CAAkB,SAAlB,CAAf;;AAEA,cAAI,CAAC,KAAKF,OAAV,EAAmB;AACfI,YAAAA,OAAO,CAACC,IAAR,CAAa,uDAAb;AACH;AACJ;;AAESC,QAAAA,MAAM,GAAS;AACrB,cAAI,CAACZ,MAAD,IAAW,CAAC,KAAKK,QAAjB,IAA6B,CAAC,KAAKC,OAAvC,EAAgD;AAEhD,eAAKO,UAAL;AACH;;AAEOA,QAAAA,UAAU,GAAS;AACvB,cAAI,CAAC,KAAKR,QAAV,EAAoB,OADG,CAGvB;;AACA,eAAKA,QAAL,CAAcS,KAAd,GAJuB,CAMvB;;AACA,cAAI,KAAKC,UAAT,EAAqB;AACjB,iBAAKC,UAAL;AACH,WATsB,CAWvB;;;AACA,cAAI,KAAKC,UAAL,IAAmB,KAAKX,OAAL,CAAaY,MAAb,GAAsB,CAA7C,EAAgD;AAC5C,iBAAKC,UAAL;AACH,WAdsB,CAgBvB;;;AACA,cAAI,KAAKC,OAAL,IAAgB,KAAKd,OAAL,CAAae,GAAb,GAAmB,CAAvC,EAA0C;AACtC,iBAAKC,OAAL;AACH,WAnBsB,CAqBvB;;;AACA,cAAI,KAAKC,cAAT,EAAyB;AACrB,iBAAKC,cAAL;AACH;AACJ;;AAEOR,QAAAA,UAAU,GAAS;AACvB,cAAI,CAAC,KAAKX,QAAV,EAAoB;AAEpB,eAAKA,QAAL,CAAcoB,WAAd,GAA4B,KAAKC,WAAjC;AACA,eAAKrB,QAAL,CAAcsB,SAAd,GAA0B,CAA1B;AAEA,gBAAMC,UAAU,GAAG,CAAnB,CANuB,CAQvB;;AACA,eAAKvB,QAAL,CAAcwB,MAAd,CAAqB,CAACD,UAAtB,EAAkC,CAAlC;AACA,eAAKvB,QAAL,CAAcyB,MAAd,CAAqBF,UAArB,EAAiC,CAAjC;AACA,eAAKvB,QAAL,CAAcwB,MAAd,CAAqB,CAArB,EAAwB,CAACD,UAAzB;AACA,eAAKvB,QAAL,CAAcyB,MAAd,CAAqB,CAArB,EAAwBF,UAAxB;AACA,eAAKvB,QAAL,CAAc0B,MAAd;AACH;;AAEOZ,QAAAA,UAAU,GAAS;AACvB,cAAI,CAAC,KAAKd,QAAV,EAAoB;AAEpB,eAAKA,QAAL,CAAcoB,WAAd,GAA4B,KAAKO,WAAjC;AACA,eAAK3B,QAAL,CAAcsB,SAAd,GAA0B,CAA1B,CAJuB,CAMvB;;AACA,eAAKtB,QAAL,CAAc4B,MAAd,CAAqB,CAArB,EAAwB,CAAxB,EAA2B,KAAK3B,OAAL,CAAaY,MAAxC;AACA,eAAKb,QAAL,CAAc0B,MAAd;AACH;;AAEOT,QAAAA,OAAO,GAAS;AACpB,cAAI,CAAC,KAAKjB,QAAV,EAAoB;AAEpB,eAAKA,QAAL,CAAcoB,WAAd,GAA4B,KAAKS,QAAjC;AACA,eAAK7B,QAAL,CAAcsB,SAAd,GAA0B,CAA1B;AAEA,gBAAMQ,aAAa,GAAG,KAAK7B,OAAL,CAAa8B,KAAb,IAAsB,CAA5C;AACA,gBAAMC,QAAQ,GAAG,KAAK/B,OAAL,CAAae,GAAb,IAAoB,CAArC;AACA,gBAAMiB,SAAS,GAAGC,IAAI,CAACC,GAAL,CAAS,KAAKlC,OAAL,CAAaY,MAAb,GAAsB,EAA/B,EAAmC,EAAnC,CAAlB,CARoB,CAQsC;AAE1D;;AACA,gBAAMuB,UAAU,GAAG,CAACN,aAAa,GAAGE,QAAQ,GAAG,CAA3B,GAA+B,EAAhC,IAAsCE,IAAI,CAACG,EAA3C,GAAgD,GAAnE,CAXoB,CAWoD;;AACxE,gBAAMC,QAAQ,GAAG,CAACR,aAAa,GAAGE,QAAQ,GAAG,CAA3B,GAA+B,EAAhC,IAAsCE,IAAI,CAACG,EAA3C,GAAgD,GAAjE,CAZoB,CAcpB;;AACA,gBAAME,QAAQ,GAAGL,IAAI,CAACC,GAAL,CAAS,CAAT,EAAYD,IAAI,CAACM,KAAL,CAAWR,QAAQ,GAAG,CAAtB,CAAZ,CAAjB,CAfoB,CAeoC;;AACxD,gBAAMS,SAAS,GAAG,CAACH,QAAQ,GAAGF,UAAZ,IAA0BG,QAA5C;;AAEA,eAAK,IAAIG,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGH,QAApB,EAA8BG,CAAC,EAA/B,EAAmC;AAC/B,kBAAMC,MAAM,GAAGP,UAAU,GAAGK,SAAS,GAAGC,CAAxC;AACA,kBAAME,MAAM,GAAGR,UAAU,GAAGK,SAAS,IAAIC,CAAC,GAAG,CAAR,CAArC;AAEA,kBAAMG,EAAE,GAAGX,IAAI,CAACY,GAAL,CAASH,MAAT,IAAmBV,SAA9B;AACA,kBAAMc,EAAE,GAAGb,IAAI,CAACc,GAAL,CAASL,MAAT,IAAmBV,SAA9B;AACA,kBAAMgB,EAAE,GAAGf,IAAI,CAACY,GAAL,CAASF,MAAT,IAAmBX,SAA9B;AACA,kBAAMiB,EAAE,GAAGhB,IAAI,CAACc,GAAL,CAASJ,MAAT,IAAmBX,SAA9B;AAEA,iBAAKjC,QAAL,CAAcwB,MAAd,CAAqBqB,EAArB,EAAyBE,EAAzB;AACA,iBAAK/C,QAAL,CAAcyB,MAAd,CAAqBwB,EAArB,EAAyBC,EAAzB;AACH,WA7BmB,CA+BpB;;;AACA,gBAAMC,UAAU,GAAG,CAAnB;AACA,gBAAMC,MAAM,GAAGlB,IAAI,CAACY,GAAL,CAASV,UAAT,IAAuBH,SAAtC;AACA,gBAAMoB,MAAM,GAAGnB,IAAI,CAACc,GAAL,CAASZ,UAAT,IAAuBH,SAAtC;AACA,gBAAMqB,IAAI,GAAGpB,IAAI,CAACY,GAAL,CAASR,QAAT,IAAqBL,SAAlC;AACA,gBAAMsB,IAAI,GAAGrB,IAAI,CAACc,GAAL,CAASV,QAAT,IAAqBL,SAAlC,CApCoB,CAsCpB;;AACA,eAAKjC,QAAL,CAAcwB,MAAd,CAAqB4B,MAAM,GAAGD,UAA9B,EAA0CE,MAA1C;AACA,eAAKrD,QAAL,CAAcyB,MAAd,CAAqB2B,MAAM,GAAGD,UAA9B,EAA0CE,MAA1C;AACA,eAAKrD,QAAL,CAAcwB,MAAd,CAAqB4B,MAArB,EAA6BC,MAAM,GAAGF,UAAtC;AACA,eAAKnD,QAAL,CAAcyB,MAAd,CAAqB2B,MAArB,EAA6BC,MAAM,GAAGF,UAAtC,EA1CoB,CA4CpB;;AACA,eAAKnD,QAAL,CAAcwB,MAAd,CAAqB8B,IAAI,GAAGH,UAA5B,EAAwCI,IAAxC;AACA,eAAKvD,QAAL,CAAcyB,MAAd,CAAqB6B,IAAI,GAAGH,UAA5B,EAAwCI,IAAxC;AACA,eAAKvD,QAAL,CAAcwB,MAAd,CAAqB8B,IAArB,EAA2BC,IAAI,GAAGJ,UAAlC;AACA,eAAKnD,QAAL,CAAcyB,MAAd,CAAqB6B,IAArB,EAA2BC,IAAI,GAAGJ,UAAlC;AAEA,eAAKnD,QAAL,CAAc0B,MAAd;AACH;;AAEOP,QAAAA,cAAc,GAAS;AAC3B,cAAI,CAAC,KAAKnB,QAAN,IAAkB,KAAKC,OAAL,CAAauD,KAAb,IAAsB,CAA5C,EAA+C;AAE/C,eAAKxD,QAAL,CAAcoB,WAAd,GAA4B,KAAKqC,cAAjC;AACA,eAAKzD,QAAL,CAAcsB,SAAd,GAA0B,CAA1B,CAJ2B,CAM3B;;AACA,gBAAMQ,aAAa,GAAG,KAAK7B,OAAL,CAAa8B,KAAb,IAAsB,CAA5C,CAP2B,CAOoB;;AAC/C,gBAAMC,QAAQ,GAAG,KAAK/B,OAAL,CAAae,GAAb,IAAoB,CAArC,CAR2B,CAQa;AAExC;;AACA,gBAAM0C,cAAc,GAAG,KAAKzD,OAAL,CAAauD,KAAb,GAAqB,CAArB,GAAyBxB,QAAQ,IAAI,KAAK/B,OAAL,CAAauD,KAAb,GAAqB,CAAzB,CAAjC,GAA+D,CAAtF;AACA,gBAAMpB,UAAU,GAAGN,aAAa,GAAGE,QAAQ,GAAG,CAA9C,CAZ2B,CAYsB;;AAEjD,eAAK,IAAIU,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKzC,OAAL,CAAauD,KAAjC,EAAwCd,CAAC,EAAzC,EAA6C;AACzC,gBAAIiB,WAAJ;;AAEA,gBAAI,KAAK1D,OAAL,CAAauD,KAAb,KAAuB,CAA3B,EAA8B;AAC1BG,cAAAA,WAAW,GAAG7B,aAAd,CAD0B,CACG;AAChC,aAFD,MAEO;AACH6B,cAAAA,WAAW,GAAGvB,UAAU,GAAIsB,cAAc,GAAGhB,CAA7C;AACH,aAPwC,CASzC;;;AACA,kBAAMkB,QAAQ,GAAG,CAACD,WAAW,GAAG,EAAf,IAAqBzB,IAAI,CAACG,EAA1B,GAA+B,GAAhD,CAVyC,CAYzC;;AACA,kBAAMwB,IAAI,GAAG3B,IAAI,CAACY,GAAL,CAASc,QAAT,CAAb;AACA,kBAAME,IAAI,GAAG5B,IAAI,CAACc,GAAL,CAASY,QAAT,CAAb,CAdyC,CAgBzC;;AACA,kBAAMR,MAAM,GAAGS,IAAI,GAAG,KAAK5D,OAAL,CAAaY,MAAnC;AACA,kBAAMwC,MAAM,GAAGS,IAAI,GAAG,KAAK7D,OAAL,CAAaY,MAAnC,CAlByC,CAoBzC;AACA;;AACA,kBAAMkD,UAAU,GAAG,EAAnB;AACA,kBAAMC,WAAW,GAAG,KAAK/D,OAAL,CAAagE,KAAb,IAAsB,CAA1C,CAvByC,CAuBI;;AAC7C,kBAAMC,WAAW,GAAGhC,IAAI,CAACC,GAAL,CAAS4B,UAAT,EAAqBA,UAAU,GAAGC,WAAb,GAA2B,KAAKG,UAArD,CAApB;AAEA,kBAAMb,IAAI,GAAGF,MAAM,GAAGS,IAAI,GAAGK,WAA7B;AACA,kBAAMX,IAAI,GAAGF,MAAM,GAAGS,IAAI,GAAGI,WAA7B,CA3ByC,CA6BzC;;AACA,iBAAKlE,QAAL,CAAcwB,MAAd,CAAqB4B,MAArB,EAA6BC,MAA7B;AACA,iBAAKrD,QAAL,CAAcyB,MAAd,CAAqB6B,IAArB,EAA2BC,IAA3B,EA/ByC,CAiCzC;;AACA,iBAAKa,aAAL,CAAmBd,IAAnB,EAAyBC,IAAzB,EAA+BM,IAA/B,EAAqCC,IAArC;AACH;;AAED,eAAK9D,QAAL,CAAc0B,MAAd;AACH;;AAEO0C,QAAAA,aAAa,CAACd,IAAD,EAAeC,IAAf,EAA6BM,IAA7B,EAA2CC,IAA3C,EAA+D;AAChF,cAAI,CAAC,KAAK9D,QAAV,EAAoB;AAEpB,gBAAMqE,SAAS,GAAG,CAAlB,CAHgF,CAKhF;;AACA,gBAAMC,UAAU,GAAGpC,IAAI,CAACG,EAAL,GAAU,CAA7B,CANgF,CAMhD;AAEhC;;AACA,gBAAMkC,KAAK,GAAGjB,IAAI,GAAGe,SAAS,IAAIR,IAAI,GAAG3B,IAAI,CAACY,GAAL,CAASwB,UAAT,CAAP,GAA8BR,IAAI,GAAG5B,IAAI,CAACc,GAAL,CAASsB,UAAT,CAAzC,CAA9B;AACA,gBAAME,KAAK,GAAGjB,IAAI,GAAGc,SAAS,IAAIP,IAAI,GAAG5B,IAAI,CAACY,GAAL,CAASwB,UAAT,CAAP,GAA8BT,IAAI,GAAG3B,IAAI,CAACc,GAAL,CAASsB,UAAT,CAAzC,CAA9B,CAVgF,CAYhF;;AACA,gBAAMG,MAAM,GAAGnB,IAAI,GAAGe,SAAS,IAAIR,IAAI,GAAG3B,IAAI,CAACY,GAAL,CAAS,CAACwB,UAAV,CAAP,GAA+BR,IAAI,GAAG5B,IAAI,CAACc,GAAL,CAAS,CAACsB,UAAV,CAA1C,CAA/B;AACA,gBAAMI,MAAM,GAAGnB,IAAI,GAAGc,SAAS,IAAIP,IAAI,GAAG5B,IAAI,CAACY,GAAL,CAAS,CAACwB,UAAV,CAAP,GAA+BT,IAAI,GAAG3B,IAAI,CAACc,GAAL,CAAS,CAACsB,UAAV,CAA1C,CAA/B,CAdgF,CAgBhF;;AACA,eAAKtE,QAAL,CAAcwB,MAAd,CAAqB8B,IAArB,EAA2BC,IAA3B;AACA,eAAKvD,QAAL,CAAcyB,MAAd,CAAqB8C,KAArB,EAA4BC,KAA5B;AACA,eAAKxE,QAAL,CAAcwB,MAAd,CAAqB8B,IAArB,EAA2BC,IAA3B;AACA,eAAKvD,QAAL,CAAcyB,MAAd,CAAqBgD,MAArB,EAA6BC,MAA7B;AACH;;AA1OuC,O,6EAEvC7E,Q;;;;;iBAC4B,I;;yFAE5BA,Q;;;;;iBACgC,I;;qFAEhCA,Q;;;;;iBAC4B,I;;kFAE5BA,Q;;;;;iBACyB,I;;sFAEzBA,Q;;;;;iBAC2BJ,KAAK,CAACkF,I;;yFAEjC9E,Q;;;;;iBAC8BJ,KAAK,CAACmF,G;;sFAEpC/E,Q;;;;;iBAC2BJ,KAAK,CAACoF,K;;mFAEjChF,Q;;;;;iBACwBJ,KAAK,CAACqF,M;;qFAE9BjF,Q;;;;;iBAC2B,G", "sourcesContent": ["/**\n * Emitter Gizmo Component\n * This component provides visual debugging for Emitter components in the scene view\n * It should be added to the same node as the Emitter component\n */\n\nimport { _decorator, Component, Color, Graphics } from 'cc';\nimport { EDITOR } from 'cc/env';\nconst { ccclass, property, executeInEditMode } = _decorator;\n\n@ccclass('EmitterGizmo')\n@executeInEditMode(true)\nexport class EmitterGizmo extends Component {\n\n    @property\n    public showRadius: boolean = true;\n\n    @property\n    public showDirections: boolean = true;\n\n    @property\n    public showCenter: boolean = true;\n\n    @property\n    public showArc: boolean = true;\n\n    @property\n    public radiusColor: Color = Color.GRAY;\n\n    @property\n    public directionColor: Color = Color.RED;\n\n    @property\n    public centerColor: Color = Color.WHITE;\n\n    @property\n    public arcColor: Color = Color.YELLOW;\n\n    @property\n    public speedScale: number = 1.0;\n\n    private graphics: Graphics | null = null;\n    private emitter: any = null;\n\n    protected onLoad(): void {\n        if (!EDITOR) return;\n\n        // Get or create Graphics component\n        this.graphics = this.getComponent(Graphics) || this.addComponent(Graphics);\n\n        // Get Emitter component\n        this.emitter = this.getComponent('Emitter');\n\n        if (!this.emitter) {\n            console.warn('EmitterGizmo: No Emitter component found on this node');\n        }\n    }\n\n    protected update(): void {\n        if (!EDITOR || !this.graphics || !this.emitter) return;\n\n        this.drawGizmos();\n    }\n\n    private drawGizmos(): void {\n        if (!this.graphics) return;\n\n        // Clear previous drawings\n        this.graphics.clear();\n\n        // Draw center point\n        if (this.showCenter) {\n            this.drawCenter();\n        }\n\n        // Draw radius circle\n        if (this.showRadius && this.emitter.radius > 0) {\n            this.drawRadius();\n        }\n\n        // Draw arc visualization\n        if (this.showArc && this.emitter.arc > 0) {\n            this.drawArc();\n        }\n\n        // Draw direction arrows\n        if (this.showDirections) {\n            this.drawDirections();\n        }\n    }\n\n    private drawCenter(): void {\n        if (!this.graphics) return;\n\n        this.graphics.strokeColor = this.centerColor;\n        this.graphics.lineWidth = 2;\n\n        const centerSize = 8;\n\n        // Draw cross at center\n        this.graphics.moveTo(-centerSize, 0);\n        this.graphics.lineTo(centerSize, 0);\n        this.graphics.moveTo(0, -centerSize);\n        this.graphics.lineTo(0, centerSize);\n        this.graphics.stroke();\n    }\n\n    private drawRadius(): void {\n        if (!this.graphics) return;\n\n        this.graphics.strokeColor = this.radiusColor;\n        this.graphics.lineWidth = 1;\n\n        // Draw radius circle\n        this.graphics.circle(0, 0, this.emitter.radius);\n        this.graphics.stroke();\n    }\n\n    private drawArc(): void {\n        if (!this.graphics) return;\n\n        this.graphics.strokeColor = this.arcColor;\n        this.graphics.lineWidth = 2;\n\n        const baseDirection = this.emitter.angle || 0;\n        const totalArc = this.emitter.arc || 0;\n        const arcRadius = Math.max(this.emitter.radius + 20, 50); // Arc drawn outside radius\n\n        // Calculate start and end angles for the arc\n        const startAngle = (baseDirection - totalArc / 2 + 90) * Math.PI / 180; // +90 for Cocos coordinate system\n        const endAngle = (baseDirection + totalArc / 2 + 90) * Math.PI / 180;\n\n        // Draw arc using line segments\n        const segments = Math.max(8, Math.floor(totalArc / 5)); // More segments for larger arcs\n        const angleStep = (endAngle - startAngle) / segments;\n\n        for (let i = 0; i < segments; i++) {\n            const angle1 = startAngle + angleStep * i;\n            const angle2 = startAngle + angleStep * (i + 1);\n\n            const x1 = Math.cos(angle1) * arcRadius;\n            const y1 = Math.sin(angle1) * arcRadius;\n            const x2 = Math.cos(angle2) * arcRadius;\n            const y2 = Math.sin(angle2) * arcRadius;\n\n            this.graphics.moveTo(x1, y1);\n            this.graphics.lineTo(x2, y2);\n        }\n\n        // Draw arc end markers\n        const markerSize = 8;\n        const startX = Math.cos(startAngle) * arcRadius;\n        const startY = Math.sin(startAngle) * arcRadius;\n        const endX = Math.cos(endAngle) * arcRadius;\n        const endY = Math.sin(endAngle) * arcRadius;\n\n        // Start marker\n        this.graphics.moveTo(startX - markerSize, startY);\n        this.graphics.lineTo(startX + markerSize, startY);\n        this.graphics.moveTo(startX, startY - markerSize);\n        this.graphics.lineTo(startX, startY + markerSize);\n\n        // End marker\n        this.graphics.moveTo(endX - markerSize, endY);\n        this.graphics.lineTo(endX + markerSize, endY);\n        this.graphics.moveTo(endX, endY - markerSize);\n        this.graphics.lineTo(endX, endY + markerSize);\n\n        this.graphics.stroke();\n    }\n\n    private drawDirections(): void {\n        if (!this.graphics || this.emitter.count <= 0) return;\n\n        this.graphics.strokeColor = this.directionColor;\n        this.graphics.lineWidth = 2;\n\n        // Use arc property for spread calculation, angle for direction\n        const baseDirection = this.emitter.angle || 0; // Base direction from angle property\n        const totalArc = this.emitter.arc || 0; // Total arc to spread bullets across\n\n        // Calculate angle per bullet based on arc and count\n        const anglePerBullet = this.emitter.count > 1 ? totalArc / (this.emitter.count - 1) : 0;\n        const startAngle = baseDirection - totalArc / 2; // Start from base direction minus half arc\n\n        for (let i = 0; i < this.emitter.count; i++) {\n            let bulletAngle: number;\n\n            if (this.emitter.count === 1) {\n                bulletAngle = baseDirection; // Single bullet goes in base direction\n            } else {\n                bulletAngle = startAngle + (anglePerBullet * i);\n            }\n\n            // Convert angle to radians (0 degrees = up in Cocos Creator)\n            const angleRad = (bulletAngle + 90) * Math.PI / 180;\n\n            // Calculate direction vector\n            const dirX = Math.cos(angleRad);\n            const dirY = Math.sin(angleRad);\n\n            // Start position (at radius distance from center)\n            const startX = dirX * this.emitter.radius;\n            const startY = dirY * this.emitter.radius;\n\n            // Calculate arrow length based on speed factor\n            // Base length of 30 pixels, scaled by speed factor and speedScale property\n            const baseLength = 30;\n            const speedFactor = this.emitter.speed || 1; // Default to 1 if speed is 0 or undefined\n            const arrowLength = Math.max(baseLength, baseLength * speedFactor * this.speedScale);\n\n            const endX = startX + dirX * arrowLength;\n            const endY = startY + dirY * arrowLength;\n\n            // Draw arrow line\n            this.graphics.moveTo(startX, startY);\n            this.graphics.lineTo(endX, endY);\n\n            // Draw arrow head\n            this.drawArrowHead(endX, endY, dirX, dirY);\n        }\n\n        this.graphics.stroke();\n    }\n\n    private drawArrowHead(endX: number, endY: number, dirX: number, dirY: number): void {\n        if (!this.graphics) return;\n\n        const arrowSize = 8;\n\n        // Calculate arrow head points\n        const arrowAngle = Math.PI / 6; // 30 degrees\n\n        // Left arrow point\n        const leftX = endX - arrowSize * (dirX * Math.cos(arrowAngle) - dirY * Math.sin(arrowAngle));\n        const leftY = endY - arrowSize * (dirY * Math.cos(arrowAngle) + dirX * Math.sin(arrowAngle));\n\n        // Right arrow point\n        const rightX = endX - arrowSize * (dirX * Math.cos(-arrowAngle) - dirY * Math.sin(-arrowAngle));\n        const rightY = endY - arrowSize * (dirY * Math.cos(-arrowAngle) + dirX * Math.sin(-arrowAngle));\n\n        // Draw arrow head lines\n        this.graphics.moveTo(endX, endY);\n        this.graphics.lineTo(leftX, leftY);\n        this.graphics.moveTo(endX, endY);\n        this.graphics.lineTo(rightX, rightY);\n    }\n}\n"]}