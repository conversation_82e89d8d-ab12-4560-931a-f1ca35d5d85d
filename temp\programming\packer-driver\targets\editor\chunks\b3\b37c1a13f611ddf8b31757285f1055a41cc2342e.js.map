{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/gizmos/EmitterGizmo.ts"], "names": ["Vec3", "Color", "EmitterGizmo", "onDrawGizmos", "target", "gizmos", "node", "worldPos", "worldPosition", "radius", "color", "GRAY", "strokeWidth", "circle", "anglePerBullet", "count", "angle", "startAngle", "YELLOW", "drawArc", "RED", "i", "bulletAngle", "angleRad", "Math", "PI", "dirX", "cos", "dirY", "sin", "startPos", "x", "y", "z", "<PERSON><PERSON><PERSON><PERSON>", "max", "endPos", "line", "drawArrowHead", "WHITE", "centerSize", "text", "textPos", "center", "totalAngle", "segments", "angleStep", "angle1", "angle2", "x1", "y1", "x2", "y2", "start", "end", "size", "dir", "subtract", "normalize", "perp", "arrowPoint1", "arrowPoint2", "scaleAndAdd"], "mappings": ";;;;;;;;;;AAMSA,MAAAA,I,OAAAA,I;AAAMC,MAAAA,K,OAAAA,K;;;;;;AANf;AACA;AACA;AACA;AACA;;;AAIA;;;AACMC,MAAAA,Y,GAAe;AAEjB;AACJ;AACA;AACA;AACA;AACIC,QAAAA,YAAY,CAACC,MAAD,EAAcC,MAAd,EAA2B;AACnC,cAAI,CAACD,MAAM,CAACE,IAAZ,EAAkB,OADiB,CAGnC;;AACA,gBAAMC,QAAQ,GAAGH,MAAM,CAACE,IAAP,CAAYE,aAA7B,CAJmC,CAMnC;;AACA,cAAIJ,MAAM,CAACK,MAAP,GAAgB,CAApB,EAAuB;AACnBJ,YAAAA,MAAM,CAACK,KAAP,GAAeT,KAAK,CAACU,IAArB;AACAN,YAAAA,MAAM,CAACO,WAAP,GAAqB,CAArB;AACAP,YAAAA,MAAM,CAACQ,MAAP,CAAcN,QAAd,EAAwBH,MAAM,CAACK,MAA/B;AACH,WAXkC,CAanC;;;AACA,gBAAMK,cAAc,GAAGV,MAAM,CAACW,KAAP,GAAe,CAAf,GAAmBX,MAAM,CAACY,KAAP,IAAgBZ,MAAM,CAACW,KAAP,GAAe,CAA/B,CAAnB,GAAuD,CAA9E;AACA,gBAAME,UAAU,GAAG,CAACb,MAAM,CAACY,KAAR,GAAgB,CAAnC,CAfmC,CAeG;AAEtC;;AACA,cAAIZ,MAAM,CAACY,KAAP,GAAe,CAAf,IAAoBZ,MAAM,CAACK,MAAP,GAAgB,CAAxC,EAA2C;AACvCJ,YAAAA,MAAM,CAACK,KAAP,GAAeT,KAAK,CAACiB,MAArB;AACAb,YAAAA,MAAM,CAACO,WAAP,GAAqB,CAArB;AACA,iBAAKO,OAAL,CAAad,MAAb,EAAqBE,QAArB,EAA+BH,MAAM,CAACK,MAAtC,EAA8CQ,UAA9C,EAA0Db,MAAM,CAACY,KAAjE,EAAwE,EAAxE;AACH,WAtBkC,CAwBnC;;;AACAX,UAAAA,MAAM,CAACK,KAAP,GAAeT,KAAK,CAACmB,GAArB;AACAf,UAAAA,MAAM,CAACO,WAAP,GAAqB,CAArB;;AAEA,eAAK,IAAIS,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGjB,MAAM,CAACW,KAA3B,EAAkCM,CAAC,EAAnC,EAAuC;AACnC,gBAAIC,WAAJ;;AAEA,gBAAIlB,MAAM,CAACW,KAAP,KAAiB,CAArB,EAAwB;AACpBO,cAAAA,WAAW,GAAG,CAAd,CADoB,CACH;AACpB,aAFD,MAEO;AACHA,cAAAA,WAAW,GAAGL,UAAU,GAAIH,cAAc,GAAGO,CAA7C;AACH,aAPkC,CASnC;;;AACA,kBAAME,QAAQ,GAAID,WAAW,GAAGE,IAAI,CAACC,EAApB,GAA0B,GAA3C,CAVmC,CAYnC;;AACA,kBAAMC,IAAI,GAAGF,IAAI,CAACG,GAAL,CAASJ,QAAT,CAAb;AACA,kBAAMK,IAAI,GAAGJ,IAAI,CAACK,GAAL,CAASN,QAAT,CAAb,CAdmC,CAgBnC;;AACA,kBAAMO,QAAQ,GAAG,IAAI9B,IAAJ,CACbO,QAAQ,CAACwB,CAAT,GAAaL,IAAI,GAAGtB,MAAM,CAACK,MADd,EAEbF,QAAQ,CAACyB,CAAT,GAAaJ,IAAI,GAAGxB,MAAM,CAACK,MAFd,EAGbF,QAAQ,CAAC0B,CAHI,CAAjB,CAjBmC,CAuBnC;;AACA,kBAAMC,WAAW,GAAGV,IAAI,CAACW,GAAL,CAAS,EAAT,EAAa/B,MAAM,CAACK,MAAP,GAAgB,GAA7B,CAApB;AACA,kBAAM2B,MAAM,GAAG,IAAIpC,IAAJ,CACX8B,QAAQ,CAACC,CAAT,GAAaL,IAAI,GAAGQ,WADT,EAEXJ,QAAQ,CAACE,CAAT,GAAaJ,IAAI,GAAGM,WAFT,EAGXJ,QAAQ,CAACG,CAHE,CAAf,CAzBmC,CA+BnC;;AACA5B,YAAAA,MAAM,CAACgC,IAAP,CAAYP,QAAZ,EAAsBM,MAAtB,EAhCmC,CAkCnC;;AACA,iBAAKE,aAAL,CAAmBjC,MAAnB,EAA2ByB,QAA3B,EAAqCM,MAArC,EAA6C,EAA7C;AACH,WAhEkC,CAkEnC;;;AACA/B,UAAAA,MAAM,CAACK,KAAP,GAAeT,KAAK,CAACsC,KAArB;AACAlC,UAAAA,MAAM,CAACO,WAAP,GAAqB,CAArB;AACA,gBAAM4B,UAAU,GAAG,CAAnB;AACAnC,UAAAA,MAAM,CAACgC,IAAP,CACI,IAAIrC,IAAJ,CAASO,QAAQ,CAACwB,CAAT,GAAaS,UAAtB,EAAkCjC,QAAQ,CAACyB,CAA3C,EAA8CzB,QAAQ,CAAC0B,CAAvD,CADJ,EAEI,IAAIjC,IAAJ,CAASO,QAAQ,CAACwB,CAAT,GAAaS,UAAtB,EAAkCjC,QAAQ,CAACyB,CAA3C,EAA8CzB,QAAQ,CAAC0B,CAAvD,CAFJ;AAIA5B,UAAAA,MAAM,CAACgC,IAAP,CACI,IAAIrC,IAAJ,CAASO,QAAQ,CAACwB,CAAlB,EAAqBxB,QAAQ,CAACyB,CAAT,GAAaQ,UAAlC,EAA8CjC,QAAQ,CAAC0B,CAAvD,CADJ,EAEI,IAAIjC,IAAJ,CAASO,QAAQ,CAACwB,CAAlB,EAAqBxB,QAAQ,CAACyB,CAAT,GAAaQ,UAAlC,EAA8CjC,QAAQ,CAAC0B,CAAvD,CAFJ,EA1EmC,CA+EnC;;AACA,cAAI5B,MAAM,CAACoC,IAAP,IAAerC,MAAM,CAACY,KAAP,GAAe,CAAlC,EAAqC;AACjCX,YAAAA,MAAM,CAACK,KAAP,GAAeT,KAAK,CAACsC,KAArB;AACA,kBAAMG,OAAO,GAAG,IAAI1C,IAAJ,CAASO,QAAQ,CAACwB,CAAlB,EAAqBxB,QAAQ,CAACyB,CAAT,GAAa5B,MAAM,CAACK,MAApB,GAA6B,EAAlD,EAAsDF,QAAQ,CAAC0B,CAA/D,CAAhB;AACA5B,YAAAA,MAAM,CAACoC,IAAP,CAAYC,OAAZ,EAAsB,UAAStC,MAAM,CAACY,KAAM,aAAYZ,MAAM,CAACW,KAAM,aAAYX,MAAM,CAACK,MAAO,EAA/F;AACH;AACJ,SA5FgB;;AA8FjB;AACJ;AACA;AACIU,QAAAA,OAAO,CAACd,MAAD,EAAcsC,MAAd,EAA4BlC,MAA5B,EAA4CQ,UAA5C,EAAgE2B,UAAhE,EAAoFC,QAApF,EAA4G;AAC/G,gBAAMC,SAAS,GAAGF,UAAU,GAAGC,QAA/B;;AAEA,eAAK,IAAIxB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGwB,QAApB,EAA8BxB,CAAC,EAA/B,EAAmC;AAC/B,kBAAM0B,MAAM,GAAG,CAAC9B,UAAU,GAAG6B,SAAS,GAAGzB,CAA1B,IAA+BG,IAAI,CAACC,EAApC,GAAyC,GAAxD;AACA,kBAAMuB,MAAM,GAAG,CAAC/B,UAAU,GAAG6B,SAAS,IAAIzB,CAAC,GAAG,CAAR,CAAvB,IAAqCG,IAAI,CAACC,EAA1C,GAA+C,GAA9D;AAEA,kBAAMwB,EAAE,GAAGN,MAAM,CAACZ,CAAP,GAAWP,IAAI,CAACG,GAAL,CAASoB,MAAT,IAAmBtC,MAAzC;AACA,kBAAMyC,EAAE,GAAGP,MAAM,CAACX,CAAP,GAAWR,IAAI,CAACK,GAAL,CAASkB,MAAT,IAAmBtC,MAAzC;AACA,kBAAM0C,EAAE,GAAGR,MAAM,CAACZ,CAAP,GAAWP,IAAI,CAACG,GAAL,CAASqB,MAAT,IAAmBvC,MAAzC;AACA,kBAAM2C,EAAE,GAAGT,MAAM,CAACX,CAAP,GAAWR,IAAI,CAACK,GAAL,CAASmB,MAAT,IAAmBvC,MAAzC;AAEAJ,YAAAA,MAAM,CAACgC,IAAP,CACI,IAAIrC,IAAJ,CAASiD,EAAT,EAAaC,EAAb,EAAiBP,MAAM,CAACV,CAAxB,CADJ,EAEI,IAAIjC,IAAJ,CAASmD,EAAT,EAAaC,EAAb,EAAiBT,MAAM,CAACV,CAAxB,CAFJ;AAIH;AACJ,SAlHgB;;AAoHjB;AACJ;AACA;AACIK,QAAAA,aAAa,CAACjC,MAAD,EAAcgD,KAAd,EAA2BC,GAA3B,EAAsCC,IAAtC,EAA0D;AACnE;AACA,gBAAMC,GAAG,GAAG,IAAIxD,IAAJ,EAAZ;AACAA,UAAAA,IAAI,CAACyD,QAAL,CAAcD,GAAd,EAAmBF,GAAnB,EAAwBD,KAAxB;AACAG,UAAAA,GAAG,CAACE,SAAJ,GAJmE,CAMnE;;AACA,gBAAMC,IAAI,GAAG,IAAI3D,IAAJ,CAAS,CAACwD,GAAG,CAACxB,CAAd,EAAiBwB,GAAG,CAACzB,CAArB,EAAwB,CAAxB,CAAb,CAPmE,CASnE;;AACA,gBAAM6B,WAAW,GAAG,IAAI5D,IAAJ,EAApB;AACA,gBAAM6D,WAAW,GAAG,IAAI7D,IAAJ,EAApB;AAEAA,UAAAA,IAAI,CAAC8D,WAAL,CAAiBF,WAAjB,EAA8BN,GAA9B,EAAmCE,GAAnC,EAAwC,CAACD,IAAzC;AACAvD,UAAAA,IAAI,CAAC8D,WAAL,CAAiBF,WAAjB,EAA8BA,WAA9B,EAA2CD,IAA3C,EAAiDJ,IAAI,GAAG,GAAxD;AAEAvD,UAAAA,IAAI,CAAC8D,WAAL,CAAiBD,WAAjB,EAA8BP,GAA9B,EAAmCE,GAAnC,EAAwC,CAACD,IAAzC;AACAvD,UAAAA,IAAI,CAAC8D,WAAL,CAAiBD,WAAjB,EAA8BA,WAA9B,EAA2CF,IAA3C,EAAiD,CAACJ,IAAD,GAAQ,GAAzD,EAjBmE,CAmBnE;;AACAlD,UAAAA,MAAM,CAACgC,IAAP,CAAYiB,GAAZ,EAAiBM,WAAjB;AACAvD,UAAAA,MAAM,CAACgC,IAAP,CAAYiB,GAAZ,EAAiBO,WAAjB;AACH;;AA7IgB,O,EAgJrB;;yBACe3D,Y", "sourcesContent": ["/**\n * Gizmo for Emitter component\n * This file should be placed in the assets/gizmos/ folder\n * to be automatically recognized by Cocos Creator editor\n */\n\nimport { Vec3, Color } from 'cc';\n\n// Gizmo definition for Emitter component\nconst EmitterGizmo = {\n    \n    /**\n     * Called when drawing gizmos for the Emitter component\n     * @param target The Emitter component instance\n     * @param gizmos The gizmos drawing API\n     */\n    onDrawGizmos(target: any, gizmos: any) {\n        if (!target.node) return;\n\n        // Get world position\n        const worldPos = target.node.worldPosition;\n        \n        // Draw emission radius circle (optional, for reference)\n        if (target.radius > 0) {\n            gizmos.color = Color.GRAY;\n            gizmos.strokeWidth = 1;\n            gizmos.circle(worldPos, target.radius);\n        }\n\n        // Calculate angle per bullet\n        const anglePerBullet = target.count > 1 ? target.angle / (target.count - 1) : 0;\n        const startAngle = -target.angle / 2; // Start from negative half angle\n\n        // Draw emission arc\n        if (target.angle > 0 && target.radius > 0) {\n            gizmos.color = Color.YELLOW;\n            gizmos.strokeWidth = 2;\n            this.drawArc(gizmos, worldPos, target.radius, startAngle, target.angle, 32);\n        }\n\n        // Draw bullet direction arrows\n        gizmos.color = Color.RED;\n        gizmos.strokeWidth = 2;\n\n        for (let i = 0; i < target.count; i++) {\n            let bulletAngle: number;\n            \n            if (target.count === 1) {\n                bulletAngle = 0; // Single bullet goes straight\n            } else {\n                bulletAngle = startAngle + (anglePerBullet * i);\n            }\n\n            // Convert angle to radians\n            const angleRad = (bulletAngle * Math.PI) / 180;\n            \n            // Calculate direction vector\n            const dirX = Math.cos(angleRad);\n            const dirY = Math.sin(angleRad);\n            \n            // Start position (at radius distance from center)\n            const startPos = new Vec3(\n                worldPos.x + dirX * target.radius,\n                worldPos.y + dirY * target.radius,\n                worldPos.z\n            );\n            \n            // End position (arrow length)\n            const arrowLength = Math.max(50, target.radius * 0.5);\n            const endPos = new Vec3(\n                startPos.x + dirX * arrowLength,\n                startPos.y + dirY * arrowLength,\n                startPos.z\n            );\n            \n            // Draw arrow line\n            gizmos.line(startPos, endPos);\n            \n            // Draw arrow head\n            this.drawArrowHead(gizmos, startPos, endPos, 10);\n        }\n\n        // Draw center point\n        gizmos.color = Color.WHITE;\n        gizmos.strokeWidth = 3;\n        const centerSize = 5;\n        gizmos.line(\n            new Vec3(worldPos.x - centerSize, worldPos.y, worldPos.z),\n            new Vec3(worldPos.x + centerSize, worldPos.y, worldPos.z)\n        );\n        gizmos.line(\n            new Vec3(worldPos.x, worldPos.y - centerSize, worldPos.z),\n            new Vec3(worldPos.x, worldPos.y + centerSize, worldPos.z)\n        );\n\n        // Draw angle indicator text (if supported)\n        if (gizmos.text && target.angle > 0) {\n            gizmos.color = Color.WHITE;\n            const textPos = new Vec3(worldPos.x, worldPos.y + target.radius + 20, worldPos.z);\n            gizmos.text(textPos, `Angle: ${target.angle}°\\nCount: ${target.count}\\nRadius: ${target.radius}`);\n        }\n    },\n\n    /**\n     * Draw an arc using line segments\n     */\n    drawArc(gizmos: any, center: Vec3, radius: number, startAngle: number, totalAngle: number, segments: number): void {\n        const angleStep = totalAngle / segments;\n        \n        for (let i = 0; i < segments; i++) {\n            const angle1 = (startAngle + angleStep * i) * Math.PI / 180;\n            const angle2 = (startAngle + angleStep * (i + 1)) * Math.PI / 180;\n            \n            const x1 = center.x + Math.cos(angle1) * radius;\n            const y1 = center.y + Math.sin(angle1) * radius;\n            const x2 = center.x + Math.cos(angle2) * radius;\n            const y2 = center.y + Math.sin(angle2) * radius;\n            \n            gizmos.line(\n                new Vec3(x1, y1, center.z),\n                new Vec3(x2, y2, center.z)\n            );\n        }\n    },\n\n    /**\n     * Draw arrow head at the end of a line\n     */\n    drawArrowHead(gizmos: any, start: Vec3, end: Vec3, size: number): void {\n        // Calculate direction vector\n        const dir = new Vec3();\n        Vec3.subtract(dir, end, start);\n        dir.normalize();\n        \n        // Calculate perpendicular vector\n        const perp = new Vec3(-dir.y, dir.x, 0);\n        \n        // Arrow head points\n        const arrowPoint1 = new Vec3();\n        const arrowPoint2 = new Vec3();\n        \n        Vec3.scaleAndAdd(arrowPoint1, end, dir, -size);\n        Vec3.scaleAndAdd(arrowPoint1, arrowPoint1, perp, size * 0.5);\n        \n        Vec3.scaleAndAdd(arrowPoint2, end, dir, -size);\n        Vec3.scaleAndAdd(arrowPoint2, arrowPoint2, perp, -size * 0.5);\n        \n        // Draw arrow head lines\n        gizmos.line(end, arrowPoint1);\n        gizmos.line(end, arrowPoint2);\n    }\n};\n\n// Export the gizmo\nexport default EmitterGizmo;\n"]}