{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/gizmos/EmitterGizmo.ts"], "names": ["_decorator", "Component", "Color", "Graphics", "EDITOR", "ccclass", "property", "executeInEditMode", "EmitterGizmo", "graphics", "emitter", "onLoad", "getComponent", "addComponent", "console", "warn", "update", "drawGizmos", "clear", "showCenter", "drawCenter", "showRadius", "radius", "drawRadius", "showArc", "arc", "drawArc", "showDirections", "drawDirections", "strokeColor", "centerColor", "lineWidth", "centerSize", "moveTo", "lineTo", "stroke", "radiusColor", "circle", "arcColor", "baseDirection", "angle", "totalArc", "startRadius", "speedFactor", "speed", "endRadius", "speedScale", "startAngle", "Math", "PI", "endAngle", "segments", "max", "floor", "angleStep", "i", "angle1", "angle2", "x1", "cos", "y1", "sin", "x2", "y2", "connectionLines", "t", "startX", "startY", "endX", "endY", "count", "directionColor", "anglePerBullet", "bulletAngle", "angleRad", "dirX", "dirY", "baseLength", "<PERSON><PERSON><PERSON><PERSON>", "drawArrowHead", "arrowSize", "arrowAngle", "leftX", "leftY", "rightX", "rightY", "GRAY", "RED", "WHITE", "YELLOW"], "mappings": ";;;;;;;;;;;;;;;;AAMSA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,Q,OAAAA,Q;;AAC9BC,MAAAA,M,UAAAA,M;;;;;;AAPT;AACA;AACA;AACA;AACA;;;;;OAIM;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,QAAX;AAAqBC,QAAAA;AAArB,O,GAA2CP,U;;8BAIpCQ,Y,WAFZH,OAAO,CAAC,cAAD,C,UACPE,iBAAiB,CAAC,IAAD,C,0CADlB,MAEaC,YAFb,SAEkCP,SAFlC,CAE4C;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,eA6BhCQ,QA7BgC,GA6BJ,IA7BI;AAAA,eA8BhCC,OA9BgC,GA8BjB,IA9BiB;AAAA;;AAgC9BC,QAAAA,MAAM,GAAS;AACrB,cAAI,CAACP,MAAL,EAAa,OADQ,CAGrB;;AACA,eAAKK,QAAL,GAAgB,KAAKG,YAAL,CAAkBT,QAAlB,KAA+B,KAAKU,YAAL,CAAkBV,QAAlB,CAA/C,CAJqB,CAMrB;;AACA,eAAKO,OAAL,GAAe,KAAKE,YAAL,CAAkB,SAAlB,CAAf;;AAEA,cAAI,CAAC,KAAKF,OAAV,EAAmB;AACfI,YAAAA,OAAO,CAACC,IAAR,CAAa,uDAAb;AACH;AACJ;;AAESC,QAAAA,MAAM,GAAS;AACrB,cAAI,CAACZ,MAAD,IAAW,CAAC,KAAKK,QAAjB,IAA6B,CAAC,KAAKC,OAAvC,EAAgD;AAEhD,eAAKO,UAAL;AACH;;AAEOA,QAAAA,UAAU,GAAS;AACvB,cAAI,CAAC,KAAKR,QAAV,EAAoB,OADG,CAGvB;;AACA,eAAKA,QAAL,CAAcS,KAAd,GAJuB,CAMvB;;AACA,cAAI,KAAKC,UAAT,EAAqB;AACjB,iBAAKC,UAAL;AACH,WATsB,CAWvB;;;AACA,cAAI,KAAKC,UAAL,IAAmB,KAAKX,OAAL,CAAaY,MAAb,GAAsB,CAA7C,EAAgD;AAC5C,iBAAKC,UAAL;AACH,WAdsB,CAgBvB;;;AACA,cAAI,KAAKC,OAAL,IAAgB,KAAKd,OAAL,CAAae,GAAb,GAAmB,CAAvC,EAA0C;AACtC,iBAAKC,OAAL;AACH,WAnBsB,CAqBvB;;;AACA,cAAI,KAAKC,cAAT,EAAyB;AACrB,iBAAKC,cAAL;AACH;AACJ;;AAEOR,QAAAA,UAAU,GAAS;AACvB,cAAI,CAAC,KAAKX,QAAV,EAAoB;AAEpB,eAAKA,QAAL,CAAcoB,WAAd,GAA4B,KAAKC,WAAjC;AACA,eAAKrB,QAAL,CAAcsB,SAAd,GAA0B,CAA1B;AAEA,gBAAMC,UAAU,GAAG,CAAnB,CANuB,CAQvB;;AACA,eAAKvB,QAAL,CAAcwB,MAAd,CAAqB,CAACD,UAAtB,EAAkC,CAAlC;AACA,eAAKvB,QAAL,CAAcyB,MAAd,CAAqBF,UAArB,EAAiC,CAAjC;AACA,eAAKvB,QAAL,CAAcwB,MAAd,CAAqB,CAArB,EAAwB,CAACD,UAAzB;AACA,eAAKvB,QAAL,CAAcyB,MAAd,CAAqB,CAArB,EAAwBF,UAAxB;AACA,eAAKvB,QAAL,CAAc0B,MAAd;AACH;;AAEOZ,QAAAA,UAAU,GAAS;AACvB,cAAI,CAAC,KAAKd,QAAV,EAAoB;AAEpB,eAAKA,QAAL,CAAcoB,WAAd,GAA4B,KAAKO,WAAjC;AACA,eAAK3B,QAAL,CAAcsB,SAAd,GAA0B,CAA1B,CAJuB,CAMvB;;AACA,eAAKtB,QAAL,CAAc4B,MAAd,CAAqB,CAArB,EAAwB,CAAxB,EAA2B,KAAK3B,OAAL,CAAaY,MAAxC;AACA,eAAKb,QAAL,CAAc0B,MAAd;AACH;;AAEOT,QAAAA,OAAO,GAAS;AACpB,cAAI,CAAC,KAAKjB,QAAV,EAAoB;AAEpB,eAAKA,QAAL,CAAcoB,WAAd,GAA4B,KAAKS,QAAjC;AACA,eAAK7B,QAAL,CAAcsB,SAAd,GAA0B,CAA1B;AAEA,gBAAMQ,aAAa,GAAG,KAAK7B,OAAL,CAAa8B,KAAb,IAAsB,CAA5C;AACA,gBAAMC,QAAQ,GAAG,KAAK/B,OAAL,CAAae,GAAb,IAAoB,CAArC;AACA,gBAAMiB,WAAW,GAAG,KAAKhC,OAAL,CAAaY,MAAb,IAAuB,CAA3C,CARoB,CAQ0B;;AAC9C,gBAAMqB,WAAW,GAAG,KAAKjC,OAAL,CAAakC,KAAb,IAAsB,CAA1C;AACA,gBAAMC,SAAS,GAAGH,WAAW,GAAIC,WAAW,GAAG,KAAKG,UAAnB,GAAgC,EAAjE,CAVoB,CAUkD;AAEtE;;AACA,gBAAMC,UAAU,GAAG,CAACR,aAAa,GAAGE,QAAQ,GAAG,CAA3B,GAA+B,EAAhC,IAAsCO,IAAI,CAACC,EAA3C,GAAgD,GAAnE,CAboB,CAaoD;;AACxE,gBAAMC,QAAQ,GAAG,CAACX,aAAa,GAAGE,QAAQ,GAAG,CAA3B,GAA+B,EAAhC,IAAsCO,IAAI,CAACC,EAA3C,GAAgD,GAAjE,CAdoB,CAgBpB;;AACA,gBAAME,QAAQ,GAAGH,IAAI,CAACI,GAAL,CAAS,CAAT,EAAYJ,IAAI,CAACK,KAAL,CAAWZ,QAAQ,GAAG,CAAtB,CAAZ,CAAjB,CAjBoB,CAiBoC;;AACxD,gBAAMa,SAAS,GAAG,CAACJ,QAAQ,GAAGH,UAAZ,IAA0BI,QAA5C;;AAEA,eAAK,IAAII,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGJ,QAApB,EAA8BI,CAAC,EAA/B,EAAmC;AAC/B,kBAAMC,MAAM,GAAGT,UAAU,GAAGO,SAAS,GAAGC,CAAxC;AACA,kBAAME,MAAM,GAAGV,UAAU,GAAGO,SAAS,IAAIC,CAAC,GAAG,CAAR,CAArC;AAEA,kBAAMG,EAAE,GAAGV,IAAI,CAACW,GAAL,CAASH,MAAT,IAAmBd,WAA9B;AACA,kBAAMkB,EAAE,GAAGZ,IAAI,CAACa,GAAL,CAASL,MAAT,IAAmBd,WAA9B;AACA,kBAAMoB,EAAE,GAAGd,IAAI,CAACW,GAAL,CAASF,MAAT,IAAmBf,WAA9B;AACA,kBAAMqB,EAAE,GAAGf,IAAI,CAACa,GAAL,CAASJ,MAAT,IAAmBf,WAA9B;AAEA,iBAAKjC,QAAL,CAAcwB,MAAd,CAAqByB,EAArB,EAAyBE,EAAzB;AACA,iBAAKnD,QAAL,CAAcyB,MAAd,CAAqB4B,EAArB,EAAyBC,EAAzB;AACH,WA/BmB,CAiCpB;;;AACA,eAAK,IAAIR,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGJ,QAApB,EAA8BI,CAAC,EAA/B,EAAmC;AAC/B,kBAAMC,MAAM,GAAGT,UAAU,GAAGO,SAAS,GAAGC,CAAxC;AACA,kBAAME,MAAM,GAAGV,UAAU,GAAGO,SAAS,IAAIC,CAAC,GAAG,CAAR,CAArC;AAEA,kBAAMG,EAAE,GAAGV,IAAI,CAACW,GAAL,CAASH,MAAT,IAAmBX,SAA9B;AACA,kBAAMe,EAAE,GAAGZ,IAAI,CAACa,GAAL,CAASL,MAAT,IAAmBX,SAA9B;AACA,kBAAMiB,EAAE,GAAGd,IAAI,CAACW,GAAL,CAASF,MAAT,IAAmBZ,SAA9B;AACA,kBAAMkB,EAAE,GAAGf,IAAI,CAACa,GAAL,CAASJ,MAAT,IAAmBZ,SAA9B;AAEA,iBAAKpC,QAAL,CAAcwB,MAAd,CAAqByB,EAArB,EAAyBE,EAAzB;AACA,iBAAKnD,QAAL,CAAcyB,MAAd,CAAqB4B,EAArB,EAAyBC,EAAzB;AACH,WA7CmB,CA+CpB;;;AACA,gBAAMC,eAAe,GAAG,CAAxB,CAhDoB,CAgDO;;AAC3B,eAAK,IAAIT,CAAC,GAAG,CAAb,EAAgBA,CAAC,IAAIS,eAArB,EAAsCT,CAAC,EAAvC,EAA2C;AACvC,kBAAMU,CAAC,GAAGV,CAAC,GAAGS,eAAd;AACA,kBAAMxB,KAAK,GAAGO,UAAU,GAAG,CAACG,QAAQ,GAAGH,UAAZ,IAA0BkB,CAArD;AAEA,kBAAMC,MAAM,GAAGlB,IAAI,CAACW,GAAL,CAASnB,KAAT,IAAkBE,WAAjC;AACA,kBAAMyB,MAAM,GAAGnB,IAAI,CAACa,GAAL,CAASrB,KAAT,IAAkBE,WAAjC;AACA,kBAAM0B,IAAI,GAAGpB,IAAI,CAACW,GAAL,CAASnB,KAAT,IAAkBK,SAA/B;AACA,kBAAMwB,IAAI,GAAGrB,IAAI,CAACa,GAAL,CAASrB,KAAT,IAAkBK,SAA/B;AAEA,iBAAKpC,QAAL,CAAcwB,MAAd,CAAqBiC,MAArB,EAA6BC,MAA7B;AACA,iBAAK1D,QAAL,CAAcyB,MAAd,CAAqBkC,IAArB,EAA2BC,IAA3B;AACH;;AAED,eAAK5D,QAAL,CAAc0B,MAAd;AACH;;AAEOP,QAAAA,cAAc,GAAS;AAC3B,cAAI,CAAC,KAAKnB,QAAN,IAAkB,KAAKC,OAAL,CAAa4D,KAAb,IAAsB,CAA5C,EAA+C;AAE/C,eAAK7D,QAAL,CAAcoB,WAAd,GAA4B,KAAK0C,cAAjC;AACA,eAAK9D,QAAL,CAAcsB,SAAd,GAA0B,CAA1B,CAJ2B,CAM3B;;AACA,gBAAMQ,aAAa,GAAG,KAAK7B,OAAL,CAAa8B,KAAb,IAAsB,CAA5C,CAP2B,CAOoB;;AAC/C,gBAAMC,QAAQ,GAAG,KAAK/B,OAAL,CAAae,GAAb,IAAoB,CAArC,CAR2B,CAQa;AAExC;;AACA,gBAAM+C,cAAc,GAAG,KAAK9D,OAAL,CAAa4D,KAAb,GAAqB,CAArB,GAAyB7B,QAAQ,IAAI,KAAK/B,OAAL,CAAa4D,KAAb,GAAqB,CAAzB,CAAjC,GAA+D,CAAtF;AACA,gBAAMvB,UAAU,GAAGR,aAAa,GAAGE,QAAQ,GAAG,CAA9C,CAZ2B,CAYsB;;AAEjD,eAAK,IAAIc,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAK7C,OAAL,CAAa4D,KAAjC,EAAwCf,CAAC,EAAzC,EAA6C;AACzC,gBAAIkB,WAAJ;;AAEA,gBAAI,KAAK/D,OAAL,CAAa4D,KAAb,KAAuB,CAA3B,EAA8B;AAC1BG,cAAAA,WAAW,GAAGlC,aAAd,CAD0B,CACG;AAChC,aAFD,MAEO;AACHkC,cAAAA,WAAW,GAAG1B,UAAU,GAAIyB,cAAc,GAAGjB,CAA7C;AACH,aAPwC,CASzC;;;AACA,kBAAMmB,QAAQ,GAAG,CAACD,WAAW,GAAG,EAAf,IAAqBzB,IAAI,CAACC,EAA1B,GAA+B,GAAhD,CAVyC,CAYzC;;AACA,kBAAM0B,IAAI,GAAG3B,IAAI,CAACW,GAAL,CAASe,QAAT,CAAb;AACA,kBAAME,IAAI,GAAG5B,IAAI,CAACa,GAAL,CAASa,QAAT,CAAb,CAdyC,CAgBzC;;AACA,kBAAMR,MAAM,GAAGS,IAAI,GAAG,KAAKjE,OAAL,CAAaY,MAAnC;AACA,kBAAM6C,MAAM,GAAGS,IAAI,GAAG,KAAKlE,OAAL,CAAaY,MAAnC,CAlByC,CAoBzC;AACA;;AACA,kBAAMuD,UAAU,GAAG,EAAnB;AACA,kBAAMlC,WAAW,GAAG,KAAKjC,OAAL,CAAakC,KAAb,IAAsB,CAA1C,CAvByC,CAuBI;;AAC7C,kBAAMkC,WAAW,GAAG9B,IAAI,CAACI,GAAL,CAASyB,UAAT,EAAqBA,UAAU,GAAGlC,WAAb,GAA2B,KAAKG,UAArD,CAApB;AAEA,kBAAMsB,IAAI,GAAGF,MAAM,GAAGS,IAAI,GAAGG,WAA7B;AACA,kBAAMT,IAAI,GAAGF,MAAM,GAAGS,IAAI,GAAGE,WAA7B,CA3ByC,CA6BzC;;AACA,iBAAKrE,QAAL,CAAcwB,MAAd,CAAqBiC,MAArB,EAA6BC,MAA7B;AACA,iBAAK1D,QAAL,CAAcyB,MAAd,CAAqBkC,IAArB,EAA2BC,IAA3B,EA/ByC,CAiCzC;;AACA,iBAAKU,aAAL,CAAmBX,IAAnB,EAAyBC,IAAzB,EAA+BM,IAA/B,EAAqCC,IAArC;AACH;;AAED,eAAKnE,QAAL,CAAc0B,MAAd;AACH;;AAEO4C,QAAAA,aAAa,CAACX,IAAD,EAAeC,IAAf,EAA6BM,IAA7B,EAA2CC,IAA3C,EAA+D;AAChF,cAAI,CAAC,KAAKnE,QAAV,EAAoB;AAEpB,gBAAMuE,SAAS,GAAG,CAAlB,CAHgF,CAKhF;;AACA,gBAAMC,UAAU,GAAGjC,IAAI,CAACC,EAAL,GAAU,CAA7B,CANgF,CAMhD;AAEhC;;AACA,gBAAMiC,KAAK,GAAGd,IAAI,GAAGY,SAAS,IAAIL,IAAI,GAAG3B,IAAI,CAACW,GAAL,CAASsB,UAAT,CAAP,GAA8BL,IAAI,GAAG5B,IAAI,CAACa,GAAL,CAASoB,UAAT,CAAzC,CAA9B;AACA,gBAAME,KAAK,GAAGd,IAAI,GAAGW,SAAS,IAAIJ,IAAI,GAAG5B,IAAI,CAACW,GAAL,CAASsB,UAAT,CAAP,GAA8BN,IAAI,GAAG3B,IAAI,CAACa,GAAL,CAASoB,UAAT,CAAzC,CAA9B,CAVgF,CAYhF;;AACA,gBAAMG,MAAM,GAAGhB,IAAI,GAAGY,SAAS,IAAIL,IAAI,GAAG3B,IAAI,CAACW,GAAL,CAAS,CAACsB,UAAV,CAAP,GAA+BL,IAAI,GAAG5B,IAAI,CAACa,GAAL,CAAS,CAACoB,UAAV,CAA1C,CAA/B;AACA,gBAAMI,MAAM,GAAGhB,IAAI,GAAGW,SAAS,IAAIJ,IAAI,GAAG5B,IAAI,CAACW,GAAL,CAAS,CAACsB,UAAV,CAAP,GAA+BN,IAAI,GAAG3B,IAAI,CAACa,GAAL,CAAS,CAACoB,UAAV,CAA1C,CAA/B,CAdgF,CAgBhF;;AACA,eAAKxE,QAAL,CAAcwB,MAAd,CAAqBmC,IAArB,EAA2BC,IAA3B;AACA,eAAK5D,QAAL,CAAcyB,MAAd,CAAqBgD,KAArB,EAA4BC,KAA5B;AACA,eAAK1E,QAAL,CAAcwB,MAAd,CAAqBmC,IAArB,EAA2BC,IAA3B;AACA,eAAK5D,QAAL,CAAcyB,MAAd,CAAqBkD,MAArB,EAA6BC,MAA7B;AACH;;AAtPuC,O,6EAEvC/E,Q;;;;;iBAC4B,I;;yFAE5BA,Q;;;;;iBACgC,I;;qFAEhCA,Q;;;;;iBAC4B,I;;kFAE5BA,Q;;;;;iBACyB,I;;sFAEzBA,Q;;;;;iBAC2BJ,KAAK,CAACoF,I;;yFAEjChF,Q;;;;;iBAC8BJ,KAAK,CAACqF,G;;sFAEpCjF,Q;;;;;iBAC2BJ,KAAK,CAACsF,K;;mFAEjClF,Q;;;;;iBACwBJ,KAAK,CAACuF,M;;qFAE9BnF,Q;;;;;iBAC2B,G", "sourcesContent": ["/**\n * Emitter Gizmo Component\n * This component provides visual debugging for Emitter components in the scene view\n * It should be added to the same node as the Emitter component\n */\n\nimport { _decorator, Component, Color, Graphics } from 'cc';\nimport { EDITOR } from 'cc/env';\nconst { ccclass, property, executeInEditMode } = _decorator;\n\n@ccclass('EmitterGizmo')\n@executeInEditMode(true)\nexport class EmitterGizmo extends Component {\n\n    @property\n    public showRadius: boolean = true;\n\n    @property\n    public showDirections: boolean = true;\n\n    @property\n    public showCenter: boolean = true;\n\n    @property\n    public showArc: boolean = true;\n\n    @property\n    public radiusColor: Color = Color.GRAY;\n\n    @property\n    public directionColor: Color = Color.RED;\n\n    @property\n    public centerColor: Color = Color.WHITE;\n\n    @property\n    public arcColor: Color = Color.YELLOW;\n\n    @property\n    public speedScale: number = 1.0;\n\n    private graphics: Graphics | null = null;\n    private emitter: any = null;\n\n    protected onLoad(): void {\n        if (!EDITOR) return;\n\n        // Get or create Graphics component\n        this.graphics = this.getComponent(Graphics) || this.addComponent(Graphics);\n\n        // Get Emitter component\n        this.emitter = this.getComponent('Emitter');\n\n        if (!this.emitter) {\n            console.warn('EmitterGizmo: No Emitter component found on this node');\n        }\n    }\n\n    protected update(): void {\n        if (!EDITOR || !this.graphics || !this.emitter) return;\n\n        this.drawGizmos();\n    }\n\n    private drawGizmos(): void {\n        if (!this.graphics) return;\n\n        // Clear previous drawings\n        this.graphics.clear();\n\n        // Draw center point\n        if (this.showCenter) {\n            this.drawCenter();\n        }\n\n        // Draw radius circle\n        if (this.showRadius && this.emitter.radius > 0) {\n            this.drawRadius();\n        }\n\n        // Draw arc visualization\n        if (this.showArc && this.emitter.arc > 0) {\n            this.drawArc();\n        }\n\n        // Draw direction arrows\n        if (this.showDirections) {\n            this.drawDirections();\n        }\n    }\n\n    private drawCenter(): void {\n        if (!this.graphics) return;\n\n        this.graphics.strokeColor = this.centerColor;\n        this.graphics.lineWidth = 2;\n\n        const centerSize = 8;\n\n        // Draw cross at center\n        this.graphics.moveTo(-centerSize, 0);\n        this.graphics.lineTo(centerSize, 0);\n        this.graphics.moveTo(0, -centerSize);\n        this.graphics.lineTo(0, centerSize);\n        this.graphics.stroke();\n    }\n\n    private drawRadius(): void {\n        if (!this.graphics) return;\n\n        this.graphics.strokeColor = this.radiusColor;\n        this.graphics.lineWidth = 1;\n\n        // Draw radius circle\n        this.graphics.circle(0, 0, this.emitter.radius);\n        this.graphics.stroke();\n    }\n\n    private drawArc(): void {\n        if (!this.graphics) return;\n\n        this.graphics.strokeColor = this.arcColor;\n        this.graphics.lineWidth = 2;\n\n        const baseDirection = this.emitter.angle || 0;\n        const totalArc = this.emitter.arc || 0;\n        const startRadius = this.emitter.radius || 0; // Start from emitter radius\n        const speedFactor = this.emitter.speed || 1;\n        const endRadius = startRadius + (speedFactor * this.speedScale * 30); // End radius based on speed\n\n        // Calculate start and end angles for the arc\n        const startAngle = (baseDirection - totalArc / 2 + 90) * Math.PI / 180; // +90 for Cocos coordinate system\n        const endAngle = (baseDirection + totalArc / 2 + 90) * Math.PI / 180;\n\n        // Draw arc at start radius (emitter radius)\n        const segments = Math.max(8, Math.floor(totalArc / 5)); // More segments for larger arcs\n        const angleStep = (endAngle - startAngle) / segments;\n\n        for (let i = 0; i < segments; i++) {\n            const angle1 = startAngle + angleStep * i;\n            const angle2 = startAngle + angleStep * (i + 1);\n\n            const x1 = Math.cos(angle1) * startRadius;\n            const y1 = Math.sin(angle1) * startRadius;\n            const x2 = Math.cos(angle2) * startRadius;\n            const y2 = Math.sin(angle2) * startRadius;\n\n            this.graphics.moveTo(x1, y1);\n            this.graphics.lineTo(x2, y2);\n        }\n\n        // Draw arc at end radius (speed-based)\n        for (let i = 0; i < segments; i++) {\n            const angle1 = startAngle + angleStep * i;\n            const angle2 = startAngle + angleStep * (i + 1);\n\n            const x1 = Math.cos(angle1) * endRadius;\n            const y1 = Math.sin(angle1) * endRadius;\n            const x2 = Math.cos(angle2) * endRadius;\n            const y2 = Math.sin(angle2) * endRadius;\n\n            this.graphics.moveTo(x1, y1);\n            this.graphics.lineTo(x2, y2);\n        }\n\n        // Draw connecting lines between start and end arcs\n        const connectionLines = 5; // Number of radial lines connecting the arcs\n        for (let i = 0; i <= connectionLines; i++) {\n            const t = i / connectionLines;\n            const angle = startAngle + (endAngle - startAngle) * t;\n\n            const startX = Math.cos(angle) * startRadius;\n            const startY = Math.sin(angle) * startRadius;\n            const endX = Math.cos(angle) * endRadius;\n            const endY = Math.sin(angle) * endRadius;\n\n            this.graphics.moveTo(startX, startY);\n            this.graphics.lineTo(endX, endY);\n        }\n\n        this.graphics.stroke();\n    }\n\n    private drawDirections(): void {\n        if (!this.graphics || this.emitter.count <= 0) return;\n\n        this.graphics.strokeColor = this.directionColor;\n        this.graphics.lineWidth = 2;\n\n        // Use arc property for spread calculation, angle for direction\n        const baseDirection = this.emitter.angle || 0; // Base direction from angle property\n        const totalArc = this.emitter.arc || 0; // Total arc to spread bullets across\n\n        // Calculate angle per bullet based on arc and count\n        const anglePerBullet = this.emitter.count > 1 ? totalArc / (this.emitter.count - 1) : 0;\n        const startAngle = baseDirection - totalArc / 2; // Start from base direction minus half arc\n\n        for (let i = 0; i < this.emitter.count; i++) {\n            let bulletAngle: number;\n\n            if (this.emitter.count === 1) {\n                bulletAngle = baseDirection; // Single bullet goes in base direction\n            } else {\n                bulletAngle = startAngle + (anglePerBullet * i);\n            }\n\n            // Convert angle to radians (0 degrees = up in Cocos Creator)\n            const angleRad = (bulletAngle + 90) * Math.PI / 180;\n\n            // Calculate direction vector\n            const dirX = Math.cos(angleRad);\n            const dirY = Math.sin(angleRad);\n\n            // Start position (at radius distance from center)\n            const startX = dirX * this.emitter.radius;\n            const startY = dirY * this.emitter.radius;\n\n            // Calculate arrow length based on speed factor\n            // Base length of 30 pixels, scaled by speed factor and speedScale property\n            const baseLength = 30;\n            const speedFactor = this.emitter.speed || 1; // Default to 1 if speed is 0 or undefined\n            const arrowLength = Math.max(baseLength, baseLength * speedFactor * this.speedScale);\n\n            const endX = startX + dirX * arrowLength;\n            const endY = startY + dirY * arrowLength;\n\n            // Draw arrow line\n            this.graphics.moveTo(startX, startY);\n            this.graphics.lineTo(endX, endY);\n\n            // Draw arrow head\n            this.drawArrowHead(endX, endY, dirX, dirY);\n        }\n\n        this.graphics.stroke();\n    }\n\n    private drawArrowHead(endX: number, endY: number, dirX: number, dirY: number): void {\n        if (!this.graphics) return;\n\n        const arrowSize = 8;\n\n        // Calculate arrow head points\n        const arrowAngle = Math.PI / 6; // 30 degrees\n\n        // Left arrow point\n        const leftX = endX - arrowSize * (dirX * Math.cos(arrowAngle) - dirY * Math.sin(arrowAngle));\n        const leftY = endY - arrowSize * (dirY * Math.cos(arrowAngle) + dirX * Math.sin(arrowAngle));\n\n        // Right arrow point\n        const rightX = endX - arrowSize * (dirX * Math.cos(-arrowAngle) - dirY * Math.sin(-arrowAngle));\n        const rightY = endY - arrowSize * (dirY * Math.cos(-arrowAngle) + dirX * Math.sin(-arrowAngle));\n\n        // Draw arrow head lines\n        this.graphics.moveTo(endX, endY);\n        this.graphics.lineTo(leftX, leftY);\n        this.graphics.moveTo(endX, endY);\n        this.graphics.lineTo(rightX, rightY);\n    }\n}\n"]}