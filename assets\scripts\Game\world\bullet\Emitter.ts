import { _decorator, Component, Node } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('Emitter')
export class Emitter extends Component {

    @property({type: Node})
    bulletPrefab: Node = null;

    // 发射角度
    @property
    angle: number = 90;

    // 发射弧度
    @property
    arc: number = 0;

    // 发射半径
    @property
    radius: number = 0;

    // 发射条数
    @property
    count: number = 1;

    // 子弹初速度
    @property
    speed: number = 1;

    // 频率(间隔多少秒发射一次)
    @property
    frequency: number = 1;

    isActive: boolean = false;

    canTrigger(): boolean {
        // 检查是否可以触发发射
        return false;
    }

    setActive(active: boolean): void {
        this.isActive = active;
        if (active) {
            this.schedule(this.emitBullet, this.frequency);
        } else {
            this.unschedule(this.emitBullet);
        }
    }

    emitBullet(): void {
        // 发射子弹

    }

    getDirection(index: number): { x: number, y: number } {
        // 计算发射方向
        const angleOffset = (this.arc / (this.count - 1)) * index - this.arc / 2;
        const radian = (this.angle + angleOffset) * (Math.PI / 180);
        return {
            x: Math.cos(radian),
            y: Math.sin(radian)
        };
    }
}
