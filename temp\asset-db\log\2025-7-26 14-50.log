2025-7-26 14:50:41-log: Cannot access game frame or container.
2025-7-26 14:50:41-debug: asset-db:require-engine-code (404ms)
2025-7-26 14:50:41-log: meshopt wasm decoder initialized
2025-7-26 14:50:41-log: [box2d]:box2d wasm lib loaded.
2025-7-26 14:50:41-log: [bullet]:bullet wasm lib loaded.
2025-7-26 14:50:41-log: Cocos Creator v3.8.6
2025-7-26 14:50:41-log: Using legacy pipeline
2025-7-26 14:50:41-log: Forward render pipeline initialized.
2025-7-26 14:50:41-debug: [Assets Memory track]: asset-db:worker-init: initEngine start:30.91MB, end 80.12MB, increase: 49.21MB
2025-7-26 14:50:42-debug: [Assets Memory track]: asset-db-plugin-register: builder start:84.35MB, end 225.24MB, increase: 140.90MB
2025-7-26 14:50:42-debug: [Assets Memory track]: asset-db-plugin-register: project start:80.86MB, end 228.89MB, increase: 148.03MB
2025-7-26 14:50:42-debug: [Assets Memory track]: asset-db-plugin-register: engine-extends start:225.49MB, end 228.69MB, increase: 3.20MB
2025-7-26 14:50:41-debug: [Assets Memory track]: asset-db-plugin-register: programming start:81.01MB, end 84.31MB, increase: 3.30MB
2025-7-26 14:50:42-debug: [Assets Memory track]: asset-db:worker-init: initPlugin start:80.14MB, end 229.51MB, increase: 149.37MB
2025-7-26 14:50:42-debug: run package(harmonyos-next) handler(enable) start
2025-7-26 14:50:42-debug: run package(harmonyos-next) handler(enable) success!
2025-7-26 14:50:42-debug: run package(honor-mini-game) handler(enable) success!
2025-7-26 14:50:42-debug: run package(huawei-agc) handler(enable) start
2025-7-26 14:50:42-debug: run package(honor-mini-game) handler(enable) start
2025-7-26 14:50:42-debug: run package(huawei-quick-game) handler(enable) start
2025-7-26 14:50:42-debug: run package(huawei-agc) handler(enable) success!
2025-7-26 14:50:42-debug: run package(ios) handler(enable) start
2025-7-26 14:50:42-debug: run package(huawei-quick-game) handler(enable) success!
2025-7-26 14:50:42-debug: run package(ios) handler(enable) success!
2025-7-26 14:50:42-debug: run package(linux) handler(enable) success!
2025-7-26 14:50:42-debug: run package(linux) handler(enable) start
2025-7-26 14:50:42-debug: run package(migu-mini-game) handler(enable) success!
2025-7-26 14:50:42-debug: run package(mac) handler(enable) success!
2025-7-26 14:50:42-debug: run package(migu-mini-game) handler(enable) start
2025-7-26 14:50:42-debug: run package(native) handler(enable) start
2025-7-26 14:50:42-debug: run package(mac) handler(enable) start
2025-7-26 14:50:42-debug: run package(ohos) handler(enable) start
2025-7-26 14:50:42-debug: run package(native) handler(enable) success!
2025-7-26 14:50:42-debug: run package(runtime-dev-tools) handler(enable) start
2025-7-26 14:50:42-debug: run package(runtime-dev-tools) handler(enable) success!
2025-7-26 14:50:42-debug: run package(oppo-mini-game) handler(enable) start
2025-7-26 14:50:42-debug: run package(oppo-mini-game) handler(enable) success!
2025-7-26 14:50:42-debug: run package(ohos) handler(enable) success!
2025-7-26 14:50:42-debug: run package(taobao-mini-game) handler(enable) success!
2025-7-26 14:50:42-debug: run package(taobao-mini-game) handler(enable) start
2025-7-26 14:50:42-debug: run package(web-desktop) handler(enable) start
2025-7-26 14:50:42-debug: run package(vivo-mini-game) handler(enable) start
2025-7-26 14:50:42-debug: run package(web-desktop) handler(enable) success!
2025-7-26 14:50:42-debug: run package(vivo-mini-game) handler(enable) success!
2025-7-26 14:50:42-debug: run package(web-mobile) handler(enable) start
2025-7-26 14:50:42-debug: run package(web-mobile) handler(enable) success!
2025-7-26 14:50:42-debug: run package(wechatgame) handler(enable) start
2025-7-26 14:50:42-debug: run package(wechatgame) handler(enable) success!
2025-7-26 14:50:42-debug: run package(windows) handler(enable) start
2025-7-26 14:50:42-debug: run package(wechatprogram) handler(enable) start
2025-7-26 14:50:42-debug: run package(windows) handler(enable) success!
2025-7-26 14:50:42-debug: run package(wechatprogram) handler(enable) success!
2025-7-26 14:50:42-debug: run package(xiaomi-quick-game) handler(enable) start
2025-7-26 14:50:42-debug: run package(cocos-service) handler(enable) start
2025-7-26 14:50:42-debug: run package(xiaomi-quick-game) handler(enable) success!
2025-7-26 14:50:42-debug: run package(cocos-service) handler(enable) success!
2025-7-26 14:50:42-debug: run package(im-plugin) handler(enable) start
2025-7-26 14:50:42-debug: run package(fix-asset-default-userdata) handler(enable) success!
2025-7-26 14:50:42-debug: run package(im-plugin) handler(enable) success!
2025-7-26 14:50:42-debug: run package(fix-asset-default-userdata) handler(enable) start
2025-7-26 14:50:42-debug: run package(localization-editor) handler(enable) start
2025-7-26 14:50:42-debug: run package(localization-editor) handler(enable) success!
2025-7-26 14:50:42-debug: asset-db:worker-init: initPlugin (1044ms)
2025-7-26 14:50:42-debug: run package(placeholder) handler(enable) start
2025-7-26 14:50:42-debug: run package(placeholder) handler(enable) success!
2025-7-26 14:50:42-debug: [Assets Memory track]: asset-db:worker-init start:30.90MB, end 235.42MB, increase: 204.52MB
2025-7-26 14:50:42-debug: Run asset db hook engine-extends:beforePreStart success!
2025-7-26 14:50:42-debug: Run asset db hook engine-extends:beforePreStart ...
2025-7-26 14:50:42-debug: Run asset db hook programming:beforePreStart success!
2025-7-26 14:50:42-debug: Run asset db hook programming:beforePreStart ...
2025-7-26 14:50:43-debug: asset-db:worker-init (1574ms)
2025-7-26 14:50:43-debug: asset-db-hook-programming-beforePreStart (52ms)
2025-7-26 14:50:43-debug: asset-db-hook-engine-extends-beforePreStart (52ms)
2025-7-26 14:50:43-debug: Preimport db internal success
2025-7-26 14:50:43-debug: Preimport db assets success
2025-7-26 14:50:43-debug: Run asset db hook programming:afterPreStart ...
2025-7-26 14:50:43-debug: starting packer-driver...
2025-7-26 14:50:43-debug: initialize scripting environment...
2025-7-26 14:50:43-debug: [[Executor]] prepare before lock
2025-7-26 14:50:43-debug: Set detail map pack:///resolution-detail-map.json: {}
2025-7-26 14:50:43-debug: [[Executor]] prepare after unlock
2025-7-26 14:50:43-debug: Run asset db hook programming:afterPreStart success!
2025-7-26 14:50:43-debug: Run asset db hook engine-extends:afterPreStart success!
2025-7-26 14:50:43-debug: Run asset db hook engine-extends:afterPreStart ...
2025-7-26 14:50:43-debug: [Assets Memory track]: asset-db:worker-init: preStart start:235.43MB, end 240.10MB, increase: 4.66MB
2025-7-26 14:50:43-debug: Start up the 'internal' database...
2025-7-26 14:50:43-debug: asset-db-hook-programming-afterPreStart (561ms)
2025-7-26 14:50:43-debug: asset-db:worker-effect-data-processing (201ms)
2025-7-26 14:50:43-debug: asset-db-hook-engine-extends-afterPreStart (201ms)
2025-7-26 14:50:43-debug: [Assets Memory track]: asset-db:worker-startup-database[internal] start:235.84MB, end 251.51MB, increase: 15.67MB
2025-7-26 14:50:43-debug: Start up the 'assets' database...
2025-7-26 14:50:43-debug: asset-db:worker-startup-database[internal] (690ms)
2025-7-26 14:50:43-debug: [Assets Memory track]: asset-db:worker-startup-database[assets] start:237.46MB, end 252.79MB, increase: 15.33MB
2025-7-26 14:50:43-debug: lazy register asset handler *
2025-7-26 14:50:43-debug: lazy register asset handler directory
2025-7-26 14:50:43-debug: lazy register asset handler spine-data
2025-7-26 14:50:43-debug: lazy register asset handler text
2025-7-26 14:50:43-debug: [Assets Memory track]: asset-db:worker-init: startup start:240.12MB, end 252.80MB, increase: 12.68MB
2025-7-26 14:50:43-debug: lazy register asset handler dragonbones
2025-7-26 14:50:43-debug: lazy register asset handler json
2025-7-26 14:50:43-debug: lazy register asset handler terrain
2025-7-26 14:50:43-debug: lazy register asset handler dragonbones-atlas
2025-7-26 14:50:43-debug: lazy register asset handler typescript
2025-7-26 14:50:43-debug: lazy register asset handler javascript
2025-7-26 14:50:43-debug: lazy register asset handler scene
2025-7-26 14:50:43-debug: lazy register asset handler prefab
2025-7-26 14:50:43-debug: lazy register asset handler buffer
2025-7-26 14:50:43-debug: lazy register asset handler sprite-frame
2025-7-26 14:50:43-debug: lazy register asset handler tiled-map
2025-7-26 14:50:43-debug: lazy register asset handler sign-image
2025-7-26 14:50:43-debug: lazy register asset handler alpha-image
2025-7-26 14:50:43-debug: lazy register asset handler texture-cube
2025-7-26 14:50:43-debug: lazy register asset handler texture
2025-7-26 14:50:43-debug: lazy register asset handler erp-texture-cube
2025-7-26 14:50:43-debug: lazy register asset handler render-texture
2025-7-26 14:50:43-debug: lazy register asset handler image
2025-7-26 14:50:43-debug: lazy register asset handler texture-cube-face
2025-7-26 14:50:43-debug: lazy register asset handler gltf
2025-7-26 14:50:43-debug: lazy register asset handler rt-sprite-frame
2025-7-26 14:50:43-debug: lazy register asset handler gltf-mesh
2025-7-26 14:50:43-debug: lazy register asset handler gltf-skeleton
2025-7-26 14:50:43-debug: lazy register asset handler gltf-animation
2025-7-26 14:50:43-debug: lazy register asset handler gltf-scene
2025-7-26 14:50:43-debug: lazy register asset handler gltf-material
2025-7-26 14:50:43-debug: lazy register asset handler gltf-embeded-image
2025-7-26 14:50:43-debug: lazy register asset handler fbx
2025-7-26 14:50:43-debug: lazy register asset handler material
2025-7-26 14:50:43-debug: lazy register asset handler physics-material
2025-7-26 14:50:43-debug: lazy register asset handler audio-clip
2025-7-26 14:50:43-debug: lazy register asset handler effect
2025-7-26 14:50:43-debug: lazy register asset handler effect-header
2025-7-26 14:50:43-debug: lazy register asset handler animation-graph
2025-7-26 14:50:43-debug: lazy register asset handler animation-graph-variant
2025-7-26 14:50:43-debug: lazy register asset handler animation-clip
2025-7-26 14:50:43-debug: lazy register asset handler animation-mask
2025-7-26 14:50:43-debug: lazy register asset handler ttf-font
2025-7-26 14:50:43-debug: lazy register asset handler particle
2025-7-26 14:50:43-debug: lazy register asset handler label-atlas
2025-7-26 14:50:43-debug: lazy register asset handler render-pipeline
2025-7-26 14:50:43-debug: lazy register asset handler sprite-atlas
2025-7-26 14:50:43-debug: lazy register asset handler render-stage
2025-7-26 14:50:43-debug: lazy register asset handler bitmap-font
2025-7-26 14:50:43-debug: lazy register asset handler instantiation-material
2025-7-26 14:50:43-debug: lazy register asset handler render-flow
2025-7-26 14:50:43-debug: lazy register asset handler auto-atlas
2025-7-26 14:50:43-debug: lazy register asset handler instantiation-mesh
2025-7-26 14:50:43-debug: lazy register asset handler instantiation-skeleton
2025-7-26 14:50:43-debug: lazy register asset handler instantiation-animation
2025-7-26 14:50:43-debug: lazy register asset handler video-clip
2025-7-26 14:50:43-debug: asset-db:worker-startup-database[assets] (650ms)
2025-7-26 14:50:43-debug: asset-db:start-database (726ms)
2025-7-26 14:50:43-debug: asset-db:ready (3919ms)
2025-7-26 14:50:43-debug: fix the bug of updateDefaultUserData
2025-7-26 14:50:43-debug: init worker message success
2025-7-26 14:50:43-debug: programming:execute-script (3ms)
2025-7-26 14:50:43-debug: [Build Memory track]: builder:worker-init start:253.35MB, end 268.80MB, increase: 15.45MB
2025-7-26 14:50:43-debug: builder:worker-init (272ms)
2025-7-26 14:51:27-debug: refresh db internal success
2025-7-26 14:51:27-debug: refresh db assets success
2025-7-26 14:51:27-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-26 14:51:27-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-26 14:51:27-debug: asset-db:refresh-all-database (57ms)
2025-7-26 14:51:27-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-26 14:51:27-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-26 14:52:05-debug: refresh db internal success
2025-7-26 14:52:05-debug: refresh db assets success
2025-7-26 14:52:05-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-26 14:52:05-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-26 14:52:05-debug: asset-db:refresh-all-database (46ms)
2025-7-26 14:52:18-debug: refresh db internal success
2025-7-26 14:52:18-debug: refresh db assets success
2025-7-26 14:52:18-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-26 14:52:18-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-26 14:52:18-debug: asset-db:refresh-all-database (67ms)
2025-7-26 14:52:18-debug: asset-db:worker-effect-data-processing (-1ms)
2025-7-26 14:52:18-debug: asset-db-hook-engine-extends-afterRefresh (-1ms)
2025-7-26 14:52:40-debug: refresh db internal success
2025-7-26 14:52:40-debug: refresh db assets success
2025-7-26 14:52:40-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-26 14:52:40-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-26 14:52:40-debug: asset-db:refresh-all-database (47ms)
2025-7-26 14:52:46-debug: refresh db internal success
2025-7-26 14:52:46-debug: refresh db assets success
2025-7-26 14:52:46-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-26 14:52:46-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-26 14:52:46-debug: asset-db:refresh-all-database (49ms)
2025-7-26 14:52:49-debug: refresh db internal success
2025-7-26 14:52:49-debug: refresh db assets success
2025-7-26 14:52:49-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-26 14:52:49-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-26 14:52:49-debug: asset-db:refresh-all-database (46ms)
2025-7-26 14:53:04-debug: run package(level-editor-extension) handler(register) start
2025-7-26 14:53:04-debug: run package(level-editor-extension) handler(register) success!
2025-7-26 14:53:22-debug: refresh db internal success
2025-7-26 14:53:22-debug: refresh db assets success
2025-7-26 14:53:22-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-26 14:53:22-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-26 14:53:22-debug: asset-db:refresh-all-database (47ms)
2025-7-26 15:00:03-debug: refresh db internal success
2025-7-26 15:00:03-debug: refresh db assets success
2025-7-26 15:00:03-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-26 15:00:03-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-26 15:00:03-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-26 15:00:03-debug: asset-db:refresh-all-database (50ms)
2025-7-26 15:00:03-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-26 15:00:04-debug: run package(level-editor-extension) handler(register) start
2025-7-26 15:00:04-debug: run package(level-editor-extension) handler(register) success!
2025-7-26 15:00:11-debug: refresh db internal success
2025-7-26 15:00:11-debug: refresh db assets success
2025-7-26 15:00:11-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-26 15:00:11-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-26 15:00:11-debug: asset-db:refresh-all-database (46ms)
2025-7-26 15:00:11-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-26 15:00:11-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-26 15:00:22-debug: refresh db internal success
2025-7-26 15:00:22-debug: refresh db assets success
2025-7-26 15:00:22-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-26 15:00:22-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-26 15:00:22-debug: asset-db:refresh-all-database (46ms)
2025-7-26 15:00:55-debug: refresh db internal success
2025-7-26 15:00:55-debug: refresh db assets success
2025-7-26 15:00:55-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-26 15:00:55-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-26 15:00:55-debug: asset-db:refresh-all-database (55ms)
2025-7-26 15:00:55-debug: asset-db:worker-effect-data-processing (-1ms)
2025-7-26 15:00:55-debug: asset-db-hook-engine-extends-afterRefresh (-1ms)
2025-7-26 15:04:08-debug: refresh db internal success
2025-7-26 15:04:08-debug: refresh db assets success
2025-7-26 15:04:08-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-26 15:04:08-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-26 15:04:08-debug: asset-db:refresh-all-database (53ms)
2025-7-26 15:04:08-debug: asset-db:worker-effect-data-processing (-1ms)
2025-7-26 15:04:14-debug: refresh db internal success
2025-7-26 15:04:14-debug: refresh db assets success
2025-7-26 15:04:14-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-26 15:04:14-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-26 15:04:14-debug: asset-db:refresh-all-database (43ms)
2025-7-26 15:04:16-debug: run package(level-editor-extension) handler(register) start
2025-7-26 15:04:16-debug: run package(level-editor-extension) handler(register) success!
2025-7-26 15:05:08-debug: refresh db internal success
2025-7-26 15:05:08-debug: refresh db assets success
2025-7-26 15:05:08-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-26 15:05:08-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-26 15:05:08-debug: asset-db:refresh-all-database (55ms)
2025-7-26 15:05:18-debug: refresh db internal success
2025-7-26 15:05:18-debug: refresh db assets success
2025-7-26 15:05:18-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-26 15:05:18-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-26 15:05:18-debug: asset-db:refresh-all-database (43ms)
2025-7-26 15:05:39-debug: refresh db internal success
2025-7-26 15:05:39-debug: refresh db assets success
2025-7-26 15:05:39-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-26 15:05:39-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-26 15:05:39-debug: asset-db:refresh-all-database (56ms)
2025-7-26 15:05:39-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-26 15:05:39-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-26 15:05:41-debug: refresh db internal success
2025-7-26 15:05:41-debug: refresh db assets success
2025-7-26 15:05:41-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-26 15:05:41-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-26 15:05:41-debug: asset-db:refresh-all-database (59ms)
2025-7-26 15:05:41-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-26 15:05:41-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-26 15:05:46-debug: refresh db internal success
2025-7-26 15:05:46-debug: refresh db assets success
2025-7-26 15:05:46-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-26 15:05:46-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-26 15:05:46-debug: asset-db:refresh-all-database (45ms)
2025-7-26 15:05:46-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-26 15:05:46-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-7-26 15:06:29-debug: refresh db internal success
2025-7-26 15:06:29-debug: refresh db assets success
2025-7-26 15:06:29-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-26 15:06:29-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-26 15:06:29-debug: asset-db:refresh-all-database (56ms)
2025-7-26 15:06:29-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-26 15:06:29-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-7-26 15:08:25-debug: refresh db internal success
2025-7-26 15:08:25-debug: refresh db assets success
2025-7-26 15:08:25-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-26 15:08:25-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-26 15:08:25-debug: asset-db:refresh-all-database (53ms)
2025-7-26 15:08:25-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-26 15:08:25-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-26 15:08:31-debug: refresh db internal success
2025-7-26 15:08:31-debug: refresh db assets success
2025-7-26 15:08:31-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-26 15:08:31-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-26 15:08:31-debug: asset-db:refresh-all-database (42ms)
2025-7-26 15:08:31-debug: asset-db:worker-effect-data-processing (-1ms)
2025-7-26 15:08:31-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-26 15:08:32-debug: run package(level-editor-extension) handler(enable) start
2025-7-26 15:08:32-debug: run package(level-editor-extension) handler(enable) success!
2025-7-26 15:14:38-debug: refresh db internal success
2025-7-26 15:14:38-debug: refresh db assets success
2025-7-26 15:14:38-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-26 15:14:38-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-26 15:14:38-debug: asset-db:refresh-all-database (53ms)
2025-7-26 15:14:38-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-26 15:14:38-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-26 15:14:43-debug: refresh db internal success
2025-7-26 15:14:43-debug: refresh db assets success
2025-7-26 15:14:43-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-26 15:14:43-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-26 15:14:43-debug: asset-db:refresh-all-database (53ms)
2025-7-26 15:14:43-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-26 15:14:43-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-26 15:14:55-debug: run package(level-editor-extension) handler(disable) start
2025-7-26 15:14:55-debug: run package(level-editor-extension) handler(disable) success!
2025-7-26 15:14:58-debug: run package(level-editor-extension) handler(enable) start
2025-7-26 15:14:58-debug: run package(level-editor-extension) handler(enable) success!
2025-7-26 15:16:48-debug: refresh db internal success
2025-7-26 15:16:48-debug: refresh db assets success
2025-7-26 15:16:48-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-26 15:16:48-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-26 15:16:48-debug: asset-db:refresh-all-database (60ms)
2025-7-26 15:18:46-debug: refresh db internal success
2025-7-26 15:18:46-debug: refresh db assets success
2025-7-26 15:18:46-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-26 15:18:46-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-26 15:18:46-debug: asset-db:refresh-all-database (55ms)
2025-7-26 15:18:46-debug: asset-db:worker-effect-data-processing (2ms)
2025-7-26 15:18:46-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-7-26 15:18:49-debug: refresh db internal success
2025-7-26 15:18:49-debug: refresh db assets success
2025-7-26 15:18:49-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-26 15:18:49-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-26 15:18:49-debug: asset-db:refresh-all-database (57ms)
2025-7-26 15:19:15-debug: refresh db internal success
2025-7-26 15:19:15-debug: %cImport%c: E:\M2Game\Client\assets\scenes
background: #aaff85; color: #000;
color: #000;
2025-7-26 15:19:15-debug: %cImport%c: E:\M2Game\Client\assets\scenes\ResUpdate.scene
background: #aaff85; color: #000;
color: #000;
2025-7-26 15:19:15-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-26 15:19:15-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-26 15:19:15-debug: refresh db assets success
2025-7-26 15:19:15-debug: asset-db:refresh-all-database (58ms)
2025-7-26 15:19:29-debug: Query all assets info in project
2025-7-26 15:19:29-debug: init custom config: keepNodeUuid: false, useCache: true
2025-7-26 15:19:29-debug: Skip compress image, progress: 0%
2025-7-26 15:19:29-debug: Init all bundles start..., progress: 0%
2025-7-26 15:19:29-debug: Num of bundles: 3..., progress: 0%
2025-7-26 15:19:29-debug: Query asset bundle start, progress: 0%
2025-7-26 15:19:29-debug: // ---- build task Query asset bundle ----
2025-7-26 15:19:29-debug: Init bundle root assets start..., progress: 0%
2025-7-26 15:19:29-debug:   Number of all scenes: 4
2025-7-26 15:19:29-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-7-26 15:19:29-debug: Init bundle root assets success..., progress: 0%
2025-7-26 15:19:29-debug:   Number of all scripts: 48
2025-7-26 15:19:29-debug:   Number of other assets: 402
2025-7-26 15:19:29-log: run build task Query asset bundle success in 5 ms√, progress: 5%
2025-7-26 15:19:29-debug: [Build Memory track]: Query asset bundle start:210.05MB, end 209.78MB, increase: -272.26KB
2025-7-26 15:19:29-debug: // ---- build task Query asset bundle ---- (5ms)
2025-7-26 15:19:29-debug: Query asset bundle start, progress: 5%
2025-7-26 15:19:29-debug: // ---- build task Query asset bundle ----
2025-7-26 15:19:29-log: run build task Query asset bundle success in √, progress: 10%
2025-7-26 15:19:29-debug: Sort some build options to settings.json start, progress: 10%
2025-7-26 15:19:29-debug: [Build Memory track]: Query asset bundle start:209.82MB, end 209.91MB, increase: 95.10KB
2025-7-26 15:19:29-debug: // ---- build task Sort some build options to settings.json ----
2025-7-26 15:19:29-debug: [Build Memory track]: Sort some build options to settings.json start:209.95MB, end 209.97MB, increase: 18.84KB
2025-7-26 15:19:29-log: run build task Sort some build options to settings.json success in √, progress: 12%
2025-7-26 15:19:29-debug: // ---- build task Fill script data to settings ----
2025-7-26 15:19:29-debug: Fill script data to settings start, progress: 12%
2025-7-26 15:19:29-debug: [Build Memory track]: Fill script data to settings start:210.00MB, end 210.02MB, increase: 18.70KB
2025-7-26 15:19:29-debug: Sort some build options to settings.json start, progress: 13%
2025-7-26 15:19:29-log: run build task Fill script data to settings success in √, progress: 13%
2025-7-26 15:19:29-debug: // ---- build task Sort some build options to settings.json ----
2025-7-26 15:19:29-debug: // ---- build task Sort some build options to settings.json ---- (2ms)
2025-7-26 15:19:29-log: run build task Sort some build options to settings.json success in 2 ms√, progress: 15%
2025-7-26 15:19:29-debug: [Build Memory track]: Sort some build options to settings.json start:210.05MB, end 210.19MB, increase: 144.98KB
2025-7-26 15:19:29-debug: Query all assets info in project
2025-7-26 15:19:29-debug: Query all assets info in project
2025-7-26 15:19:29-debug: Query all assets info in project
2025-7-26 15:19:29-debug: Query all assets info in project
2025-7-26 15:19:29-debug: init custom config: keepNodeUuid: false, useCache: true
2025-7-26 15:19:29-debug: Skip compress image, progress: 0%
2025-7-26 15:19:29-debug: init custom config: keepNodeUuid: false, useCache: true
2025-7-26 15:19:29-debug: init custom config: keepNodeUuid: false, useCache: true
2025-7-26 15:19:29-debug: Skip compress image, progress: 0%
2025-7-26 15:19:29-debug: Skip compress image, progress: 0%
2025-7-26 15:19:29-debug: init custom config: keepNodeUuid: false, useCache: true
2025-7-26 15:19:29-debug: Skip compress image, progress: 0%
2025-7-26 15:19:29-debug: Init all bundles start..., progress: 0%
2025-7-26 15:19:29-debug: Query asset bundle start, progress: 0%
2025-7-26 15:19:29-debug: // ---- build task Query asset bundle ----
2025-7-26 15:19:29-debug: Init all bundles start..., progress: 0%
2025-7-26 15:19:29-debug: Init bundle root assets start..., progress: 0%
2025-7-26 15:19:29-debug: Num of bundles: 3..., progress: 0%
2025-7-26 15:19:29-debug: Query asset bundle start, progress: 0%
2025-7-26 15:19:29-debug: Num of bundles: 3..., progress: 0%
2025-7-26 15:19:29-debug: // ---- build task Query asset bundle ----
2025-7-26 15:19:29-debug: Init all bundles start..., progress: 0%
2025-7-26 15:19:29-debug: Init bundle root assets start..., progress: 0%
2025-7-26 15:19:29-debug: Query asset bundle start, progress: 0%
2025-7-26 15:19:29-debug: // ---- build task Query asset bundle ----
2025-7-26 15:19:29-debug: Num of bundles: 3..., progress: 0%
2025-7-26 15:19:29-debug: Init all bundles start..., progress: 0%
2025-7-26 15:19:29-debug: Init bundle root assets start..., progress: 0%
2025-7-26 15:19:29-debug: Query asset bundle start, progress: 0%
2025-7-26 15:19:29-debug: Num of bundles: 3..., progress: 0%
2025-7-26 15:19:29-debug: // ---- build task Query asset bundle ----
2025-7-26 15:19:29-debug: Init bundle root assets start..., progress: 0%
2025-7-26 15:19:29-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-7-26 15:19:29-debug:   Number of all scripts: 48
2025-7-26 15:19:29-debug:   Number of all scenes: 4
2025-7-26 15:19:29-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-7-26 15:19:29-debug: Init bundle root assets success..., progress: 0%
2025-7-26 15:19:29-debug:   Number of all scenes: 4
2025-7-26 15:19:29-debug:   Number of all scripts: 48
2025-7-26 15:19:29-debug:   Number of other assets: 402
2025-7-26 15:19:29-debug: Init bundle root assets success..., progress: 0%
2025-7-26 15:19:29-debug:   Number of all scenes: 4
2025-7-26 15:19:29-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-7-26 15:19:29-debug:   Number of other assets: 402
2025-7-26 15:19:29-debug: Init bundle root assets success..., progress: 0%
2025-7-26 15:19:29-debug:   Number of other assets: 402
2025-7-26 15:19:29-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-7-26 15:19:29-debug:   Number of all scripts: 48
2025-7-26 15:19:29-debug:   Number of all scenes: 4
2025-7-26 15:19:29-debug: Init bundle root assets success..., progress: 0%
2025-7-26 15:19:29-debug:   Number of other assets: 402
2025-7-26 15:19:29-debug:   Number of all scripts: 48
2025-7-26 15:19:29-debug: // ---- build task Query asset bundle ---- (10ms)
2025-7-26 15:19:29-log: run build task Query asset bundle success in 10 ms√, progress: 5%
2025-7-26 15:19:29-debug: [Build Memory track]: Query asset bundle start:210.47MB, end 210.90MB, increase: 446.81KB
2025-7-26 15:19:29-debug: Query asset bundle start, progress: 5%
2025-7-26 15:19:29-debug: // ---- build task Query asset bundle ----
2025-7-26 15:19:29-log: run build task Query asset bundle success in √, progress: 5%
2025-7-26 15:19:29-debug: [Build Memory track]: Query asset bundle start:210.93MB, end 210.95MB, increase: 19.44KB
2025-7-26 15:19:29-debug: Query asset bundle start, progress: 5%
2025-7-26 15:19:29-debug: // ---- build task Query asset bundle ----
2025-7-26 15:19:29-log: run build task Query asset bundle success in √, progress: 5%
2025-7-26 15:19:29-debug: [Build Memory track]: Query asset bundle start:210.97MB, end 210.99MB, increase: 12.09KB
2025-7-26 15:19:29-log: run build task Query asset bundle success in √, progress: 5%
2025-7-26 15:19:29-debug: // ---- build task Query asset bundle ----
2025-7-26 15:19:29-debug: [Build Memory track]: Query asset bundle start:211.02MB, end 211.03MB, increase: 12.57KB
2025-7-26 15:19:29-debug: Query asset bundle start, progress: 5%
2025-7-26 15:19:29-debug: Query asset bundle start, progress: 5%
2025-7-26 15:19:29-debug: // ---- build task Query asset bundle ----
2025-7-26 15:19:29-debug: // ---- build task Query asset bundle ---- (1ms)
2025-7-26 15:19:29-debug: // ---- build task Sort some build options to settings.json ----
2025-7-26 15:19:29-debug: [Build Memory track]: Query asset bundle start:211.06MB, end 210.68MB, increase: -389.63KB
2025-7-26 15:19:29-log: run build task Query asset bundle success in 1 ms√, progress: 10%
2025-7-26 15:19:29-debug: Sort some build options to settings.json start, progress: 10%
2025-7-26 15:19:29-log: run build task Query asset bundle success in √, progress: 10%
2025-7-26 15:19:29-debug: // ---- build task Sort some build options to settings.json ----
2025-7-26 15:19:29-log: run build task Query asset bundle success in √, progress: 10%
2025-7-26 15:19:29-debug: Sort some build options to settings.json start, progress: 10%
2025-7-26 15:19:29-debug: // ---- build task Sort some build options to settings.json ----
2025-7-26 15:19:29-debug: Sort some build options to settings.json start, progress: 10%
2025-7-26 15:19:29-debug: // ---- build task Sort some build options to settings.json ----
2025-7-26 15:19:29-log: run build task Query asset bundle success in √, progress: 10%
2025-7-26 15:19:29-debug: Sort some build options to settings.json start, progress: 10%
2025-7-26 15:19:29-log: run build task Sort some build options to settings.json success in √, progress: 12%
2025-7-26 15:19:29-debug: Fill script data to settings start, progress: 12%
2025-7-26 15:19:29-debug: [Build Memory track]: Sort some build options to settings.json start:210.81MB, end 210.82MB, increase: 13.62KB
2025-7-26 15:19:29-debug: // ---- build task Fill script data to settings ----
2025-7-26 15:19:29-debug: // ---- build task Sort some build options to settings.json ---- (1ms)
2025-7-26 15:19:29-log: run build task Sort some build options to settings.json success in 1 ms√, progress: 12%
2025-7-26 15:19:29-debug: Fill script data to settings start, progress: 12%
2025-7-26 15:19:29-debug: // ---- build task Fill script data to settings ----
2025-7-26 15:19:29-debug: // ---- build task Sort some build options to settings.json ---- (1ms)
2025-7-26 15:19:29-log: run build task Sort some build options to settings.json success in 1 ms√, progress: 12%
2025-7-26 15:19:29-log: run build task Sort some build options to settings.json success in √, progress: 12%
2025-7-26 15:19:29-debug: // ---- build task Fill script data to settings ----
2025-7-26 15:19:29-debug: Fill script data to settings start, progress: 12%
2025-7-26 15:19:29-debug: Fill script data to settings start, progress: 12%
2025-7-26 15:19:29-debug: // ---- build task Fill script data to settings ----
2025-7-26 15:19:29-log: run build task Fill script data to settings success in √, progress: 13%
2025-7-26 15:19:29-debug: [Build Memory track]: Fill script data to settings start:210.98MB, end 211.00MB, increase: 12.86KB
2025-7-26 15:19:29-debug: Sort some build options to settings.json start, progress: 13%
2025-7-26 15:19:29-debug: // ---- build task Sort some build options to settings.json ----
2025-7-26 15:19:29-log: run build task Fill script data to settings success in √, progress: 13%
2025-7-26 15:19:29-debug: Sort some build options to settings.json start, progress: 13%
2025-7-26 15:19:29-debug: // ---- build task Sort some build options to settings.json ----
2025-7-26 15:19:29-debug: // ---- build task Fill script data to settings ---- (1ms)
2025-7-26 15:19:29-log: run build task Fill script data to settings success in 1 ms√, progress: 13%
2025-7-26 15:19:29-debug: Sort some build options to settings.json start, progress: 13%
2025-7-26 15:19:29-debug: // ---- build task Sort some build options to settings.json ----
2025-7-26 15:19:29-log: run build task Fill script data to settings success in √, progress: 13%
2025-7-26 15:19:29-debug: Sort some build options to settings.json start, progress: 13%
2025-7-26 15:19:29-debug: // ---- build task Sort some build options to settings.json ----
2025-7-26 15:19:29-debug: // ---- build task Sort some build options to settings.json ---- (1ms)
2025-7-26 15:19:29-log: run build task Sort some build options to settings.json success in 1 ms√, progress: 15%
2025-7-26 15:19:29-debug: [Build Memory track]: Sort some build options to settings.json start:211.13MB, end 210.89MB, increase: -240.82KB
2025-7-26 15:19:29-log: run build task Sort some build options to settings.json success in √, progress: 15%
2025-7-26 15:19:29-log: run build task Sort some build options to settings.json success in √, progress: 15%
2025-7-26 15:19:29-log: run build task Sort some build options to settings.json success in √, progress: 15%
2025-7-26 15:20:21-debug: run package(level-editor-extension) handler(disable) start
2025-7-26 15:20:21-debug: run package(level-editor-extension) handler(disable) success!
2025-7-26 15:20:22-debug: run package(level-editor-extension) handler(register) start
2025-7-26 15:20:22-debug: run package(level-editor-extension) handler(enable) start
2025-7-26 15:20:22-debug: run package(level-editor-extension) handler(register) success!
2025-7-26 15:20:22-debug: run package(level-editor-extension) handler(enable) success!
2025-7-26 15:20:26-debug: run package(level-editor-extension) handler(disable) start
2025-7-26 15:20:26-debug: run package(level-editor-extension) handler(disable) success!
2025-7-26 15:20:27-debug: run package(level-editor-extension) handler(enable) start
2025-7-26 15:20:27-debug: run package(level-editor-extension) handler(enable) success!
2025-7-26 15:22:03-debug: refresh db internal success
2025-7-26 15:22:03-debug: refresh db assets success
2025-7-26 15:22:03-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-26 15:22:03-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-26 15:22:03-debug: asset-db:refresh-all-database (63ms)
2025-7-26 15:22:24-debug: refresh db internal success
2025-7-26 15:22:24-debug: refresh db assets success
2025-7-26 15:22:24-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-26 15:22:24-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-26 15:22:24-debug: asset-db:refresh-all-database (62ms)
2025-7-26 15:22:24-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-26 15:23:09-debug: refresh db internal success
2025-7-26 15:23:09-debug: refresh db assets success
2025-7-26 15:23:09-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-26 15:23:09-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-26 15:23:09-debug: asset-db:refresh-all-database (58ms)
2025-7-26 15:23:09-debug: asset-db:worker-effect-data-processing (2ms)
2025-7-26 15:23:09-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-7-26 15:23:13-debug: refresh db internal success
2025-7-26 15:23:13-debug: refresh db assets success
2025-7-26 15:23:13-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-26 15:23:13-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-26 15:23:13-debug: asset-db:refresh-all-database (57ms)
2025-7-26 15:23:13-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-26 15:23:13-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-7-26 15:23:15-debug: run package(level-editor-extension) handler(disable) start
2025-7-26 15:23:15-debug: run package(level-editor-extension) handler(disable) success!
2025-7-26 15:23:16-debug: run package(level-editor-extension) handler(register) start
2025-7-26 15:23:16-debug: run package(level-editor-extension) handler(enable) success!
2025-7-26 15:23:16-debug: run package(level-editor-extension) handler(register) success!
2025-7-26 15:23:16-debug: run package(level-editor-extension) handler(enable) start
2025-7-26 15:25:19-debug: refresh db internal success
2025-7-26 15:25:19-debug: refresh db assets success
2025-7-26 15:25:19-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-26 15:25:19-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-26 15:25:19-debug: asset-db:refresh-all-database (59ms)
2025-7-26 15:25:19-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-26 15:25:19-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-26 15:25:23-debug: refresh db internal success
2025-7-26 15:25:23-debug: refresh db assets success
2025-7-26 15:25:23-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-26 15:25:23-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-26 15:25:23-debug: asset-db:refresh-all-database (53ms)
2025-7-26 15:25:23-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-26 15:25:23-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-26 15:25:27-debug: run package(level-editor-extension) handler(disable) success!
2025-7-26 15:25:27-debug: run package(level-editor-extension) handler(disable) start
2025-7-26 15:25:27-debug: run package(level-editor-extension) handler(register) start
2025-7-26 15:25:27-debug: run package(level-editor-extension) handler(enable) start
2025-7-26 15:25:27-debug: run package(level-editor-extension) handler(register) success!
2025-7-26 15:25:27-debug: run package(level-editor-extension) handler(enable) success!
2025-7-26 15:32:19-debug: refresh db internal success
2025-7-26 15:32:19-debug: refresh db assets success
2025-7-26 15:32:19-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-26 15:32:19-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-26 15:32:19-debug: asset-db:refresh-all-database (57ms)
2025-7-26 15:32:19-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-26 15:32:19-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-26 15:39:09-debug: refresh db internal success
2025-7-26 15:39:09-debug: refresh db assets success
2025-7-26 15:39:09-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-26 15:39:09-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-26 15:39:09-debug: asset-db:refresh-all-database (57ms)
2025-7-26 15:39:09-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-26 15:39:09-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-26 15:39:12-debug: refresh db internal success
2025-7-26 15:39:12-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-26 15:39:12-debug: refresh db assets success
2025-7-26 15:39:12-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-26 15:39:12-debug: asset-db:refresh-all-database (57ms)
2025-7-26 15:39:12-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-26 15:39:13-debug: run package(level-editor-extension) handler(disable) start
2025-7-26 15:39:13-debug: run package(level-editor-extension) handler(disable) success!
2025-7-26 15:39:14-debug: run package(level-editor-extension) handler(register) start
2025-7-26 15:39:14-debug: run package(level-editor-extension) handler(register) success!
2025-7-26 15:39:14-debug: run package(level-editor-extension) handler(enable) start
2025-7-26 15:39:14-debug: run package(level-editor-extension) handler(enable) success!
2025-7-26 15:39:35-debug: refresh db internal success
2025-7-26 15:39:35-debug: refresh db assets success
2025-7-26 15:39:35-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-26 15:39:35-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-26 15:39:35-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-26 15:39:35-debug: asset-db:refresh-all-database (45ms)
2025-7-26 15:39:35-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-26 15:42:18-debug: refresh db internal success
2025-7-26 15:42:18-debug: refresh db assets success
2025-7-26 15:42:18-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-26 15:42:18-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-26 15:42:18-debug: asset-db:refresh-all-database (61ms)
2025-7-26 15:42:18-debug: asset-db:worker-effect-data-processing (3ms)
2025-7-26 15:42:18-debug: asset-db-hook-engine-extends-afterRefresh (4ms)
2025-7-26 15:42:22-debug: refresh db internal success
2025-7-26 15:42:22-debug: refresh db assets success
2025-7-26 15:42:22-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-26 15:42:22-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-26 15:42:22-debug: asset-db:refresh-all-database (65ms)
2025-7-26 15:42:22-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-26 15:42:25-debug: run package(level-editor-extension) handler(disable) start
2025-7-26 15:42:25-debug: run package(level-editor-extension) handler(disable) success!
2025-7-26 15:42:25-debug: run package(level-editor-extension) handler(enable) start
2025-7-26 15:42:25-debug: run package(level-editor-extension) handler(enable) success!
2025-7-26 15:42:40-debug: refresh db internal success
2025-7-26 15:42:40-debug: refresh db assets success
2025-7-26 15:42:40-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-26 15:42:40-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-26 15:42:40-debug: asset-db:refresh-all-database (45ms)
2025-7-26 15:42:40-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-26 15:42:40-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-26 15:46:07-debug: refresh db internal success
2025-7-26 15:46:07-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-26 15:46:07-debug: refresh db assets success
2025-7-26 15:46:07-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-26 15:46:07-debug: asset-db:refresh-all-database (43ms)
2025-7-26 15:46:07-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-26 15:46:07-debug: run package(level-editor-extension) handler(disable) success!
2025-7-26 15:46:07-debug: run package(level-editor-extension) handler(disable) start
2025-7-26 15:46:08-debug: run package(level-editor-extension) handler(enable) start
2025-7-26 15:46:08-debug: run package(level-editor-extension) handler(enable) success!
2025-7-26 15:46:10-debug: refresh db internal success
2025-7-26 15:46:10-debug: refresh db assets success
2025-7-26 15:46:10-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-26 15:46:10-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-26 15:46:10-debug: asset-db:refresh-all-database (44ms)
2025-7-26 15:47:23-debug: refresh db internal success
2025-7-26 15:47:23-debug: refresh db assets success
2025-7-26 15:47:23-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-26 15:47:23-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-26 15:47:23-debug: asset-db:refresh-all-database (54ms)
2025-7-26 15:47:23-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-26 15:47:23-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-26 15:47:30-debug: Query all assets info in project
2025-7-26 15:47:30-debug: init custom config: keepNodeUuid: false, useCache: true
2025-7-26 15:47:30-debug: Skip compress image, progress: 0%
2025-7-26 15:47:30-debug: Num of bundles: 3..., progress: 0%
2025-7-26 15:47:30-debug: Init all bundles start..., progress: 0%
2025-7-26 15:47:30-debug: // ---- build task Query asset bundle ----
2025-7-26 15:47:30-debug: Query asset bundle start, progress: 0%
2025-7-26 15:47:30-debug: Init bundle root assets start..., progress: 0%
2025-7-26 15:47:30-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-7-26 15:47:30-debug:   Number of all scenes: 4
2025-7-26 15:47:30-debug:   Number of all scripts: 48
2025-7-26 15:47:30-debug: Init bundle root assets success..., progress: 0%
2025-7-26 15:47:30-debug:   Number of other assets: 402
2025-7-26 15:47:30-debug: // ---- build task Query asset bundle ---- (3ms)
2025-7-26 15:47:30-log: run build task Query asset bundle success in 3 ms√, progress: 5%
2025-7-26 15:47:30-debug: Query asset bundle start, progress: 5%
2025-7-26 15:47:30-debug: [Build Memory track]: Query asset bundle start:210.50MB, end 210.80MB, increase: 309.55KB
2025-7-26 15:47:30-debug: // ---- build task Query asset bundle ----
2025-7-26 15:47:30-debug: // ---- build task Query asset bundle ---- (2ms)
2025-7-26 15:47:30-debug: [Build Memory track]: Query asset bundle start:210.83MB, end 210.22MB, increase: -615.28KB
2025-7-26 15:47:30-debug: // ---- build task Sort some build options to settings.json ----
2025-7-26 15:47:30-debug: Sort some build options to settings.json start, progress: 10%
2025-7-26 15:47:30-log: run build task Query asset bundle success in 2 ms√, progress: 10%
2025-7-26 15:47:30-log: run build task Sort some build options to settings.json success in √, progress: 12%
2025-7-26 15:47:30-debug: [Build Memory track]: Sort some build options to settings.json start:210.25MB, end 210.27MB, increase: 16.53KB
2025-7-26 15:47:30-debug: Fill script data to settings start, progress: 12%
2025-7-26 15:47:30-debug: // ---- build task Fill script data to settings ----
2025-7-26 15:47:30-log: run build task Fill script data to settings success in √, progress: 13%
2025-7-26 15:47:30-debug: [Build Memory track]: Fill script data to settings start:210.29MB, end 210.31MB, increase: 16.87KB
2025-7-26 15:47:30-debug: Sort some build options to settings.json start, progress: 13%
2025-7-26 15:47:30-debug: // ---- build task Sort some build options to settings.json ----
2025-7-26 15:47:30-debug: // ---- build task Sort some build options to settings.json ---- (2ms)
2025-7-26 15:47:30-log: run build task Sort some build options to settings.json success in 2 ms√, progress: 15%
2025-7-26 15:47:30-debug: [Build Memory track]: Sort some build options to settings.json start:210.34MB, end 210.48MB, increase: 151.63KB
2025-7-26 15:51:56-debug: refresh db internal success
2025-7-26 15:51:56-debug: refresh db assets success
2025-7-26 15:51:56-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-26 15:51:56-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-26 15:51:56-debug: asset-db:refresh-all-database (61ms)
2025-7-26 15:51:56-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-26 15:51:56-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-26 15:52:00-debug: refresh db internal success
2025-7-26 15:52:00-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-26 15:52:00-debug: refresh db assets success
2025-7-26 15:52:00-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-26 15:52:00-debug: asset-db:refresh-all-database (62ms)
2025-7-26 15:52:00-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-26 15:52:00-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-26 15:52:02-debug: refresh db internal success
2025-7-26 15:52:02-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-26 15:52:02-debug: refresh db assets success
2025-7-26 15:52:02-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-26 15:52:02-debug: asset-db:refresh-all-database (47ms)
2025-7-26 15:52:03-debug: run package(level-editor-extension) handler(disable) start
2025-7-26 15:52:03-debug: run package(level-editor-extension) handler(disable) success!
2025-7-26 15:52:03-debug: run package(level-editor-extension) handler(enable) start
2025-7-26 15:52:03-debug: run package(level-editor-extension) handler(enable) success!
2025-7-26 15:53:15-debug: refresh db internal success
2025-7-26 15:53:15-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-26 15:53:15-debug: refresh db assets success
2025-7-26 15:53:15-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-26 15:53:15-debug: asset-db:refresh-all-database (63ms)
2025-7-26 15:53:15-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-26 15:53:15-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-7-26 15:53:19-debug: refresh db internal success
2025-7-26 15:53:19-debug: refresh db assets success
2025-7-26 15:53:19-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-26 15:53:19-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-26 15:53:19-debug: asset-db:refresh-all-database (48ms)
2025-7-26 15:53:19-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-26 15:53:19-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-26 15:53:21-debug: run package(level-editor-extension) handler(disable) success!
2025-7-26 15:53:21-debug: run package(level-editor-extension) handler(disable) start
2025-7-26 15:53:21-debug: run package(level-editor-extension) handler(enable) start
2025-7-26 15:53:21-debug: run package(level-editor-extension) handler(enable) success!
2025-7-26 15:53:32-debug: refresh db internal success
2025-7-26 15:53:32-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-26 15:53:32-debug: refresh db assets success
2025-7-26 15:53:32-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-26 15:53:32-debug: asset-db:refresh-all-database (45ms)
2025-7-26 15:53:32-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-26 15:53:32-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-26 15:53:36-debug: refresh db internal success
2025-7-26 15:53:36-debug: refresh db assets success
2025-7-26 15:53:36-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-26 15:53:36-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-26 15:53:36-debug: asset-db:refresh-all-database (43ms)
2025-7-26 15:53:36-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-26 15:53:36-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-26 15:53:59-debug: refresh db internal success
2025-7-26 15:53:59-debug: refresh db assets success
2025-7-26 15:53:59-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-26 15:53:59-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-26 15:53:59-debug: asset-db:refresh-all-database (53ms)
2025-7-26 15:53:59-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-26 15:53:59-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-26 15:57:22-debug: refresh db internal success
2025-7-26 15:57:22-debug: refresh db assets success
2025-7-26 15:57:22-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-26 15:57:22-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-26 15:57:22-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-26 15:57:22-debug: asset-db:refresh-all-database (55ms)
2025-7-26 15:57:22-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-26 15:57:25-debug: refresh db internal success
2025-7-26 15:57:25-debug: refresh db assets success
2025-7-26 15:57:25-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-26 15:57:25-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-26 15:57:25-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-26 15:57:25-debug: asset-db:refresh-all-database (65ms)
2025-7-26 15:57:25-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-26 15:57:28-debug: refresh db internal success
2025-7-26 15:57:28-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-26 15:57:28-debug: refresh db assets success
2025-7-26 15:57:28-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-26 15:57:28-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-26 15:57:28-debug: asset-db:refresh-all-database (47ms)
2025-7-26 15:57:28-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-26 15:57:30-debug: run package(level-editor-extension) handler(disable) start
2025-7-26 15:57:30-debug: run package(level-editor-extension) handler(disable) success!
2025-7-26 15:57:30-debug: run package(level-editor-extension) handler(enable) start
2025-7-26 15:57:30-debug: run package(level-editor-extension) handler(enable) success!
2025-7-26 15:58:30-debug: refresh db internal success
2025-7-26 15:58:30-debug: refresh db assets success
2025-7-26 15:58:30-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-26 15:58:30-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-26 15:58:30-debug: asset-db:refresh-all-database (58ms)
2025-7-26 15:58:30-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-26 15:58:30-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-7-26 16:09:19-debug: refresh db internal success
2025-7-26 16:09:19-debug: refresh db assets success
2025-7-26 16:09:19-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-26 16:09:19-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-26 16:09:19-debug: asset-db:refresh-all-database (53ms)
2025-7-26 16:09:19-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-26 16:09:24-debug: refresh db internal success
2025-7-26 16:09:24-debug: refresh db assets success
2025-7-26 16:09:24-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-26 16:09:24-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-26 16:09:24-debug: asset-db:refresh-all-database (56ms)
2025-7-26 16:09:24-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-26 16:09:25-debug: refresh db internal success
2025-7-26 16:09:25-debug: refresh db assets success
2025-7-26 16:09:25-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-26 16:09:25-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-26 16:09:25-debug: asset-db:refresh-all-database (44ms)
2025-7-26 16:09:25-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-26 16:09:26-debug: run package(level-editor-extension) handler(disable) start
2025-7-26 16:09:26-debug: run package(level-editor-extension) handler(disable) success!
2025-7-26 16:09:26-debug: run package(level-editor-extension) handler(enable) start
2025-7-26 16:09:26-debug: run package(level-editor-extension) handler(enable) success!
2025-7-26 16:09:47-debug: refresh db internal success
2025-7-26 16:09:47-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-26 16:09:47-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-26 16:09:47-debug: refresh db assets success
2025-7-26 16:09:47-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-26 16:09:47-debug: asset-db:refresh-all-database (44ms)
2025-7-26 16:09:47-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-26 16:14:37-debug: refresh db internal success
2025-7-26 16:14:37-debug: refresh db assets success
2025-7-26 16:14:37-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-26 16:14:37-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-26 16:14:37-debug: asset-db:refresh-all-database (57ms)
2025-7-26 16:14:37-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-26 16:14:37-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-26 16:14:38-debug: refresh db internal success
2025-7-26 16:14:38-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-26 16:14:38-debug: refresh db assets success
2025-7-26 16:14:38-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-26 16:14:38-debug: asset-db:refresh-all-database (59ms)
2025-7-26 16:14:38-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-26 16:14:38-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-26 16:14:43-debug: refresh db internal success
2025-7-26 16:14:43-debug: refresh db assets success
2025-7-26 16:14:43-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-26 16:14:43-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-26 16:14:43-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-26 16:14:43-debug: asset-db:refresh-all-database (45ms)
2025-7-26 16:14:43-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-26 16:14:44-debug: run package(level-editor-extension) handler(disable) start
2025-7-26 16:14:44-debug: run package(level-editor-extension) handler(disable) success!
2025-7-26 16:14:45-debug: run package(level-editor-extension) handler(enable) success!
2025-7-26 16:14:45-debug: run package(level-editor-extension) handler(enable) start
2025-7-26 16:15:56-debug: refresh db internal success
2025-7-26 16:15:56-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-26 16:15:56-debug: refresh db assets success
2025-7-26 16:15:56-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-26 16:15:56-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-26 16:15:56-debug: asset-db:refresh-all-database (55ms)
2025-7-26 16:15:56-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-26 16:20:21-debug: refresh db internal success
2025-7-26 16:20:21-debug: refresh db assets success
2025-7-26 16:20:21-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-26 16:20:21-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-26 16:20:21-debug: asset-db:refresh-all-database (70ms)
2025-7-26 16:20:21-debug: asset-db:worker-effect-data-processing (18ms)
2025-7-26 16:20:21-debug: asset-db-hook-engine-extends-afterRefresh (19ms)
2025-7-26 16:20:31-debug: refresh db internal success
2025-7-26 16:20:31-debug: refresh db assets success
2025-7-26 16:20:31-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-26 16:20:31-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-26 16:20:31-debug: asset-db:refresh-all-database (43ms)
2025-7-26 16:20:31-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-26 16:20:31-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-26 16:20:33-debug: run package(level-editor-extension) handler(disable) start
2025-7-26 16:20:33-debug: run package(level-editor-extension) handler(disable) success!
2025-7-26 16:20:33-debug: run package(level-editor-extension) handler(enable) start
2025-7-26 16:20:33-debug: run package(level-editor-extension) handler(enable) success!
2025-7-26 16:21:37-debug: refresh db internal success
2025-7-26 16:21:37-debug: refresh db assets success
2025-7-26 16:21:37-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-26 16:21:37-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-26 16:21:37-debug: asset-db:refresh-all-database (57ms)
2025-7-26 16:21:37-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-26 16:21:37-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-26 16:32:20-debug: refresh db internal success
2025-7-26 16:32:20-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-26 16:32:20-debug: refresh db assets success
2025-7-26 16:32:20-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-26 16:32:20-debug: asset-db:refresh-all-database (56ms)
2025-7-26 16:32:20-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-26 16:32:20-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-26 16:32:28-debug: refresh db internal success
2025-7-26 16:32:28-debug: refresh db assets success
2025-7-26 16:32:28-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-26 16:32:28-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-26 16:32:28-debug: asset-db:refresh-all-database (46ms)
2025-7-26 16:32:28-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-26 16:32:28-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-26 16:32:29-debug: run package(level-editor-extension) handler(disable) start
2025-7-26 16:32:29-debug: run package(level-editor-extension) handler(disable) success!
2025-7-26 16:32:30-debug: run package(level-editor-extension) handler(enable) start
2025-7-26 16:32:30-debug: run package(level-editor-extension) handler(enable) success!
2025-7-26 16:32:52-debug: refresh db internal success
2025-7-26 16:32:52-debug: refresh db assets success
2025-7-26 16:32:52-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-26 16:32:52-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-26 16:32:52-debug: asset-db:refresh-all-database (46ms)
2025-7-26 16:32:52-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-26 16:32:52-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-26 17:52:57-debug: refresh db internal success
2025-7-26 17:52:57-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\world
background: #aaff85; color: #000;
color: #000;
2025-7-26 17:52:57-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\world\Bootstrap.ts
background: #aaff85; color: #000;
color: #000;
2025-7-26 17:52:57-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\world\base
background: #aaff85; color: #000;
color: #000;
2025-7-26 17:52:57-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\temp_arts
background: #aaff85; color: #000;
color: #000;
2025-7-26 17:52:57-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\levels
background: #aaff85; color: #000;
color: #000;
2025-7-26 17:52:57-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\world\bullet
background: #aaff85; color: #000;
color: #000;
2025-7-26 17:52:57-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\world\index.ts
background: #aaff85; color: #000;
color: #000;
2025-7-26 17:52:57-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\world\level
background: #aaff85; color: #000;
color: #000;
2025-7-26 17:52:57-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\world\examples
background: #aaff85; color: #000;
color: #000;
2025-7-26 17:52:57-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\world\player
background: #aaff85; color: #000;
color: #000;
2025-7-26 17:52:57-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\world\WorldInitializeData.ts
background: #aaff85; color: #000;
color: #000;
2025-7-26 17:52:57-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\world\base\System.ts
background: #aaff85; color: #000;
color: #000;
2025-7-26 17:52:57-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\world\base\SystemContainer.ts
background: #aaff85; color: #000;
color: #000;
2025-7-26 17:52:57-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\world\base\TypeID.ts
background: #aaff85; color: #000;
color: #000;
2025-7-26 17:52:57-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\world\base\World.ts
background: #aaff85; color: #000;
color: #000;
2025-7-26 17:52:57-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\world\bullet\BulletSystem.ts
background: #aaff85; color: #000;
color: #000;
2025-7-26 17:52:57-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\world\player\PlayerSystem.ts
background: #aaff85; color: #000;
color: #000;
2025-7-26 17:52:57-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\world\level\LevelSystem.ts
background: #aaff85; color: #000;
color: #000;
2025-7-26 17:52:57-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\temp_arts\EasterSkyCity.png
background: #aaff85; color: #000;
color: #000;
2025-7-26 17:52:57-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\temp_arts\Halloween.png
background: #aaff85; color: #000;
color: #000;
2025-7-26 17:52:57-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\temp_arts\Hyperspace_Background.png
background: #aaff85; color: #000;
color: #000;
2025-7-26 17:52:57-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\temp_arts\Mvillage.png
background: #aaff85; color: #000;
color: #000;
2025-7-26 17:52:57-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game
background: #aaff85; color: #000;
color: #000;
2025-7-26 17:52:57-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game
background: #aaff85; color: #000;
color: #000;
2025-7-26 17:52:57-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\temp_arts\EasterSkyCity.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-26 17:52:57-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\temp_arts\Mvillage.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-26 17:52:57-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\temp_arts\Halloween.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-26 17:52:57-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\temp_arts\Hyperspace_Background.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-7-26 17:52:57-debug: refresh db assets success
2025-7-26 17:52:57-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-26 17:52:57-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-26 17:52:57-debug: asset-db:refresh-all-database (112ms)
2025-7-26 17:52:57-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-26 17:52:57-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-26 17:53:24-debug: start remove asset E:\M2Game\Client\assets\scripts\Game\world\examples...
2025-7-26 17:53:24-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\Game\world\examples
background: #ffb8b8; color: #000;
color: #000;
2025-7-26 17:53:24-debug: start refresh asset from E:\M2Game\Client\assets\scripts\Game\world\examples...
2025-7-26 17:53:24-debug: remove asset E:\M2Game\Client\assets\scripts\Game\world\examples success
2025-7-26 17:53:24-debug: refresh asset E:\M2Game\Client\assets\scripts\Game\world success
2025-7-26 17:53:24-debug: refresh db internal success
2025-7-26 17:53:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\temp_arts
background: #aaff85; color: #000;
color: #000;
2025-7-26 17:53:24-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\world
background: #aaff85; color: #000;
color: #000;
2025-7-26 17:53:24-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\world\player\PlayerSystem.ts
background: #aaff85; color: #000;
color: #000;
2025-7-26 17:53:24-debug: refresh db assets success
2025-7-26 17:53:24-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-26 17:53:24-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-26 17:53:24-debug: asset-db:refresh-all-database (52ms)
2025-7-26 17:54:38-debug: refresh db internal success
2025-7-26 17:54:38-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\world\bullet\BulletSystem.ts
background: #aaff85; color: #000;
color: #000;
2025-7-26 17:54:38-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\world\player\PlayerSystem.ts
background: #aaff85; color: #000;
color: #000;
2025-7-26 17:54:38-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-26 17:54:38-debug: refresh db assets success
2025-7-26 17:54:38-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-26 17:54:38-debug: asset-db:refresh-all-database (64ms)
2025-7-26 17:57:50-debug: refresh db internal success
2025-7-26 17:57:50-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\world\examples
background: #aaff85; color: #000;
color: #000;
2025-7-26 17:57:50-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\world\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-7-26 17:57:50-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\world
background: #aaff85; color: #000;
color: #000;
2025-7-26 17:57:50-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\world\bullet
background: #aaff85; color: #000;
color: #000;
2025-7-26 17:57:50-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\world\base\System.ts
background: #aaff85; color: #000;
color: #000;
2025-7-26 17:57:50-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\world\bullet\BulletSystem.ts
background: #aaff85; color: #000;
color: #000;
2025-7-26 17:57:50-debug: refresh db assets success
2025-7-26 17:57:50-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-26 17:57:50-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-26 17:57:50-debug: asset-db:refresh-all-database (67ms)
2025-7-26 17:57:50-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-26 17:57:50-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-26 17:57:53-debug: programming:execute-script (1ms)
2025-7-26 17:57:55-debug: start remove asset E:\M2Game\Client\assets\scripts\Game\world\bullet\Emitter.ts...
2025-7-26 17:57:55-debug: start refresh asset from E:\M2Game\Client\assets\scripts\Game\world\bullet\Emitter.ts...
2025-7-26 17:57:55-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\Game\world\bullet\Emitter.ts
background: #ffb8b8; color: #000;
color: #000;
2025-7-26 17:57:55-debug: refresh asset E:\M2Game\Client\assets\scripts\Game\world\bullet success
2025-7-26 17:57:55-debug: remove asset E:\M2Game\Client\assets\scripts\Game\world\bullet\Emitter.ts success
2025-7-26 17:57:55-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\world\bullet
background: #aaff85; color: #000;
color: #000;
2025-7-26 17:57:55-debug: refresh db internal success
2025-7-26 17:57:55-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-26 17:57:55-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-26 17:57:55-debug: refresh db assets success
2025-7-26 17:57:55-debug: asset-db:refresh-all-database (52ms)
2025-7-26 17:57:55-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-26 17:57:55-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-26 17:58:07-debug: start refresh asset from E:\M2Game\Client\assets\scripts\Game\world\bullet\Bullet.ts...
2025-7-26 17:58:07-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\world\bullet\Bullet.ts
background: #aaff85; color: #000;
color: #000;
2025-7-26 17:58:07-debug: refresh asset E:\M2Game\Client\assets\scripts\Game\world\bullet success
2025-7-26 17:58:07-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\world\bullet
background: #aaff85; color: #000;
color: #000;
2025-7-26 17:58:17-debug: start refresh asset from E:\M2Game\Client\assets\scripts\Game\world\bullet\Emitter.ts...
2025-7-26 17:58:17-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\world\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-7-26 17:58:17-debug: refresh asset E:\M2Game\Client\assets\scripts\Game\world\bullet success
2025-7-26 17:58:17-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\world\bullet
background: #aaff85; color: #000;
color: #000;
2025-7-26 18:11:25-debug: refresh db internal success
2025-7-26 18:11:25-debug: %cImport%c: E:\M2Game\Client\assets\gizmos\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-7-26 18:11:25-debug: %cImport%c: E:\M2Game\Client\assets\gizmos
background: #aaff85; color: #000;
color: #000;
2025-7-26 18:11:25-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\world\bullet\EmitterGizmosGuide.md
background: #aaff85; color: #000;
color: #000;
2025-7-26 18:11:25-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\world\bullet
background: #aaff85; color: #000;
color: #000;
2025-7-26 18:11:25-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\world\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-7-26 18:11:25-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\world\bullet\Bullet.ts
background: #aaff85; color: #000;
color: #000;
2025-7-26 18:11:25-debug: refresh db assets success
2025-7-26 18:11:25-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-26 18:11:25-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-26 18:11:25-debug: asset-db:refresh-all-database (69ms)
2025-7-26 18:11:45-debug: refresh db internal success
2025-7-26 18:11:45-debug: refresh db assets success
2025-7-26 18:11:45-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-26 18:11:45-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-26 18:11:45-debug: asset-db:refresh-all-database (47ms)
2025-7-26 18:11:45-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-26 18:11:45-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-26 18:11:48-debug: refresh db internal success
2025-7-26 18:11:49-debug: refresh db assets success
2025-7-26 18:11:49-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-26 18:11:49-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-26 18:11:49-debug: asset-db:refresh-all-database (46ms)
2025-7-26 18:11:49-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-26 18:11:49-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-26 18:12:09-debug: %cImport%c: E:\M2Game\Client\assets\scenes\LevelEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-7-26 18:12:09-debug: asset-db:reimport-asset401efd7e-bd20-4537-a13a-f25e6238c2a9 (3ms)
2025-7-26 18:12:31-debug: refresh db internal success
2025-7-26 18:12:31-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\world\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-7-26 18:12:31-debug: refresh db assets success
2025-7-26 18:12:31-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-26 18:12:31-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-26 18:12:31-debug: asset-db:refresh-all-database (64ms)
2025-7-26 18:12:31-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-7-26 18:12:31-debug: asset-db:worker-effect-data-processing (2ms)
2025-7-26 18:19:46-debug: refresh db internal success
2025-7-26 18:19:46-debug: refresh db assets success
2025-7-26 18:19:46-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-26 18:19:46-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-26 18:19:46-debug: asset-db:refresh-all-database (62ms)
2025-7-26 18:19:46-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-26 18:19:46-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-26 18:26:27-debug: refresh db internal success
2025-7-26 18:26:27-debug: %cDestroy%c: E:\M2Game\Client\assets\gizmos\Emitter.ts
background: #ffb8b8; color: #000;
color: #000;
2025-7-26 18:26:27-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\Game\world\bullet\EmitterGizmosGuide.md
background: #ffb8b8; color: #000;
color: #000;
2025-7-26 18:26:27-debug: %cImport%c: E:\M2Game\Client\assets\gizmos\EmitterGizmo.ts
background: #aaff85; color: #000;
color: #000;
2025-7-26 18:26:27-debug: %cImport%c: E:\M2Game\Client\assets\gizmos
background: #aaff85; color: #000;
color: #000;
2025-7-26 18:26:27-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\world\bullet
background: #aaff85; color: #000;
color: #000;
2025-7-26 18:26:27-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\world\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-7-26 18:26:27-debug: refresh db assets success
2025-7-26 18:26:27-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-26 18:26:27-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-26 18:26:27-debug: asset-db:refresh-all-database (65ms)
2025-7-26 18:26:27-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-7-26 18:26:34-debug: %cImport%c: E:\M2Game\Client\assets\scenes\LevelEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-7-26 18:26:34-debug: asset-db:reimport-asset401efd7e-bd20-4537-a13a-f25e6238c2a9 (2ms)
2025-7-26 18:26:35-debug: refresh db internal success
2025-7-26 18:26:35-debug: refresh db assets success
2025-7-26 18:26:35-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-26 18:26:35-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-26 18:26:35-debug: asset-db:refresh-all-database (49ms)
2025-7-26 18:27:06-debug: %cImport%c: E:\M2Game\Client\assets\scenes\LevelEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-7-26 18:27:06-debug: asset-db:reimport-asset401efd7e-bd20-4537-a13a-f25e6238c2a9 (2ms)
2025-7-26 18:29:01-debug: refresh db internal success
2025-7-26 18:29:01-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\Game\level\core
background: #ffb8b8; color: #000;
color: #000;
2025-7-26 18:29:01-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\Game\level\editor
background: #ffb8b8; color: #000;
color: #000;
2025-7-26 18:29:01-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\Game\level\events
background: #ffb8b8; color: #000;
color: #000;
2025-7-26 18:29:01-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\Game\level
background: #ffb8b8; color: #000;
color: #000;
2025-7-26 18:29:01-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\Game\level\runtime
background: #ffb8b8; color: #000;
color: #000;
2025-7-26 18:29:01-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\Game\level\core\LevelData.ts
background: #ffb8b8; color: #000;
color: #000;
2025-7-26 18:29:01-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\Game\level\core\LevelSerializer.ts
background: #ffb8b8; color: #000;
color: #000;
2025-7-26 18:29:01-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\Game\level\core\Types.ts
background: #ffb8b8; color: #000;
color: #000;
2025-7-26 18:29:01-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\Game\level\editor\LevelEditor.ts
background: #ffb8b8; color: #000;
color: #000;
2025-7-26 18:29:01-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\Game\level\editor\EditorGizmos.ts
background: #ffb8b8; color: #000;
color: #000;
2025-7-26 18:29:01-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\Game\level\editor\PathEditor.ts
background: #ffb8b8; color: #000;
color: #000;
2025-7-26 18:29:01-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\Game\level\events\EventSystem.ts
background: #ffb8b8; color: #000;
color: #000;
2025-7-26 18:29:01-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\Game\level\runtime\CameraManager.ts
background: #ffb8b8; color: #000;
color: #000;
2025-7-26 18:29:01-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\Game\level\runtime\MapLayerComponent.ts
background: #ffb8b8; color: #000;
color: #000;
2025-7-26 18:29:01-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\Game\level\runtime\LevelManager.ts
background: #ffb8b8; color: #000;
color: #000;
2025-7-26 18:29:01-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\Game\level\runtime\MapSystem.ts
background: #ffb8b8; color: #000;
color: #000;
2025-7-26 18:29:01-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\Game\level\runtime\PathFollower.ts
background: #ffb8b8; color: #000;
color: #000;
2025-7-26 18:29:01-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\Game\level\runtime\SpawnerComponent.ts
background: #ffb8b8; color: #000;
color: #000;
2025-7-26 18:29:01-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\Game\level\runtime\SpawnerSystem.ts
background: #ffb8b8; color: #000;
color: #000;
2025-7-26 18:29:01-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\Game\level\runtime\PathManager.ts
background: #ffb8b8; color: #000;
color: #000;
2025-7-26 18:29:01-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\Game\level\runtime\SubLevelComponent.ts
background: #ffb8b8; color: #000;
color: #000;
2025-7-26 18:29:01-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game
background: #aaff85; color: #000;
color: #000;
2025-7-26 18:29:01-debug: refresh db assets success
2025-7-26 18:29:01-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-26 18:29:01-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-26 18:29:01-debug: asset-db:refresh-all-database (65ms)
2025-7-26 18:29:16-debug: run package(level-editor-extension) handler(disable) start
2025-7-26 18:29:16-debug: run package(level-editor-extension) handler(disable) success!
2025-7-26 18:29:22-debug: %cImport%c: E:\M2Game\Client\assets\scenes\LevelEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-7-26 18:29:22-debug: asset-db:reimport-asset401efd7e-bd20-4537-a13a-f25e6238c2a9 (3ms)
2025-7-26 18:29:22-debug: refresh db internal success
2025-7-26 18:29:22-debug: refresh db assets success
2025-7-26 18:29:22-debug: Run asset db hook engine-extends:afterRefresh ...
2025-7-26 18:29:22-debug: Run asset db hook engine-extends:afterRefresh success!
2025-7-26 18:29:22-debug: asset-db:refresh-all-database (49ms)
2025-7-26 18:29:22-debug: asset-db:worker-effect-data-processing (1ms)
2025-7-26 18:29:22-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
