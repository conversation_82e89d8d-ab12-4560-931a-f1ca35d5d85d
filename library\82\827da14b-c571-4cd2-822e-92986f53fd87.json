{"__type__": "cc.TextAsset", "_name": "EmitterGizmosGuide", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "text": "# Emitter Gizmos Guide\n\nThis guide explains how to use and customize the gizmos for the Emitter component in Cocos Creator.\n\n## Overview\n\nThe Emitter component now includes visual gizmos that help you visualize:\n- **Emission Arc**: Shows the spread angle of bullets\n- **Bullet Directions**: Red arrows indicating each bullet's direction\n- **Emission Radius**: Gray circle showing the emission radius\n- **Center Point**: White crosshair marking the emitter center\n\n## Gizmo Components\n\n### 1. Emission Arc (Yellow)\n- Shows the total angle coverage of the emitter\n- Drawn as an arc from the center with the specified radius\n- Only visible when both `angle > 0` and `radius > 0`\n\n### 2. Bullet Direction Arrows (Red)\n- Red arrows showing the exact direction each bullet will travel\n- Number of arrows matches the `count` property\n- Arrows are evenly distributed across the emission angle\n- Arrow length is proportional to the radius (minimum 50 units)\n\n### 3. Emission Radius Circle (Gray)\n- Gray circle showing the emission radius\n- Bullets will start from points on this circle\n- Only visible when `radius > 0`\n\n### 4. Center Point (White)\n- White crosshair marking the exact center of the emitter\n- Always visible regardless of other properties\n\n## Properties Affecting Gizmos\n\n### `angle` (number)\n- **Effect**: Controls the spread angle of the emission arc\n- **Gizmo**: Determines the width of the yellow arc\n- **Range**: 0-360 degrees\n- **Note**: When `angle = 0`, all bullets fire in the same direction\n\n### `radius` (number)\n- **Effect**: Distance from center where bullets spawn\n- **Gizmo**: Size of the gray circle and starting point of arrows\n- **Note**: Larger radius = longer arrow visualization\n\n### `count` (number)\n- **Effect**: Number of bullets fired simultaneously\n- **Gizmo**: Number of red arrows displayed\n- **Distribution**: Arrows are evenly spaced across the angle\n- **Special Case**: When `count = 1`, the single bullet fires straight ahead\n\n## Usage Examples\n\n### Single Bullet Emitter\n```typescript\nangle: 0      // No spread\nradius: 50    // Spawn 50 units from center\ncount: 1      // Single bullet\n```\n**Gizmo**: One red arrow pointing straight ahead\n\n### Shotgun Pattern\n```typescript\nangle: 45     // 45-degree spread\nradius: 30    // Close to center\ncount: 5      // 5 bullets\n```\n**Gizmo**: 5 red arrows spread across a 45-degree arc\n\n### Circular Burst\n```typescript\nangle: 360    // Full circle\nradius: 100   // Large radius\ncount: 8      // 8 bullets\n```\n**Gizmo**: 8 arrows pointing in all directions around a circle\n\n## Customizing Gizmos\n\n### Colors\nYou can modify the gizmo colors in the `onDrawGizmos()` method:\n```typescript\n// Current colors\ngizmos.color = Color.YELLOW;  // Emission arc\ngizmos.color = Color.RED;     // Bullet arrows\ngizmos.color = Color.GRAY;    // Radius circle\ngizmos.color = Color.WHITE;   // Center point\n```\n\n### Line Width\nAdjust the stroke width for better visibility:\n```typescript\ngizmos.strokeWidth = 2;  // Default\ngizmos.strokeWidth = 3;  // Thicker lines\ngizmos.strokeWidth = 1;  // Thinner lines\n```\n\n### Arrow Size\nModify the arrow head size in the `drawArrowHead()` method:\n```typescript\nthis.drawArrowHead(gizmos, startPos, endPos, 10);  // Default size\nthis.drawArrowHead(gizmos, startPos, endPos, 15);  // Larger arrows\n```\n\n## Implementation Details\n\n### Two Approaches Provided\n\n1. **Component-based Gizmos** (`Emitter.ts`)\n   - Uses `@executeInEditMode(true)` decorator\n   - Gizmos drawn in `onDrawGizmos()` method\n   - Integrated directly into the component\n\n2. **Separate Gizmo File** (`assets/gizmos/Emitter.ts`)\n   - Traditional Cocos Creator gizmo approach\n   - Separate file in `assets/gizmos/` folder\n   - Automatically recognized by the editor\n\n### Key Methods\n\n- `onDrawGizmos()`: Main gizmo drawing function\n- `drawArc()`: Draws the emission arc using line segments\n- `drawArrowHead()`: Draws arrow heads for direction indicators\n\n## Troubleshooting\n\n### Gizmos Not Visible\n1. Ensure the component has `@executeInEditMode(true)` decorator\n2. Check that gizmos are enabled in the Scene view\n3. Verify the node has a valid world position\n\n### Performance Considerations\n- Gizmos only draw in edit mode, not during runtime\n- Arc drawing uses 32 segments by default (adjustable)\n- Complex gizmos may slow down the editor with many emitters\n\n### Common Issues\n- **No arrows visible**: Check that `count > 0`\n- **No arc visible**: Ensure both `angle > 0` and `radius > 0`\n- **Arrows in wrong direction**: Verify the node's rotation and world position\n\n## Best Practices\n\n1. **Use appropriate radius values**: Too small = hard to see, too large = cluttered\n2. **Limit bullet count**: High counts can make gizmos cluttered\n3. **Test different angles**: Visualize how spread affects bullet patterns\n4. **Use consistent colors**: Stick to the color scheme for clarity\n5. **Consider performance**: Disable gizmos for final builds\n\n## Future Enhancements\n\nPossible improvements to consider:\n- Dynamic gizmo scaling based on camera distance\n- Interactive gizmo handles for direct manipulation\n- Animation preview showing bullet trajectories\n- Integration with bullet physics simulation\n- Custom gizmo styles for different emitter types\n"}