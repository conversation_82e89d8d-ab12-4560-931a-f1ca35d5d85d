import { _decorator, Component, Node } from 'cc';
const { ccclass, property, executeInEditMode } = _decorator;

@ccclass('Emitter')
@executeInEditMode(true)
export class Emitter extends Component {

    @property({type: Node})
    bulletPrefab: Node = null;

    // 发射角度
    @property
    angle: number = 90;

    // 发射半径
    @property
    radius: number = 0;

    // 发射条数
    @property
    count: number = 1;

    // 子弹初速度
    @property
    speed: number = 1;

    // 频率(间隔多少秒发射一次)
    @property
    frequency: number = 1;

    isActive: boolean = false;

    canTrigger(): boolean {
        // 检查是否可以触发发射
        return false;
    }

    setActive(active: boolean): void {
        this.isActive = active;
        if (active) {
            this.schedule(this.emitBullet, this.frequency);
        } else {
            this.unschedule(this.emitBullet);
        }
    }

    emitBullet(): void {
        // 发射子弹

    }
}
